#!/bin/bash

# 使用用户编译的静态OpenCV进行编译
# 直接链接静态库，避免复杂的CMake配置

echo "========================================"
echo "使用用户编译的静态OpenCV"
echo "========================================"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ 请在MSYS2环境中运行此脚本"
    exit 1
fi

echo "✓ MSYS2环境: $MSYSTEM"

# 用户编译的静态OpenCV路径
OPENCV_STATIC_ROOT="C:/dev/opencv_static"
OPENCV_STATIC_LIB_DIR="$OPENCV_STATIC_ROOT/x64/mingw/staticlib"
OPENCV_STATIC_INCLUDE_DIR="$OPENCV_STATIC_ROOT/include"

echo "========================================"
echo "检查静态OpenCV库"
echo "========================================"

if [ ! -d "$OPENCV_STATIC_LIB_DIR" ]; then
    echo "❌ 未找到静态库目录: $OPENCV_STATIC_LIB_DIR"
    exit 1
fi

echo "✓ 找到静态库目录: $OPENCV_STATIC_LIB_DIR"

# 检查关键库文件
OPENCV_CORE_LIB="$OPENCV_STATIC_LIB_DIR/libopencv_core4130.a"
OPENCV_IMGPROC_LIB="$OPENCV_STATIC_LIB_DIR/libopencv_imgproc4130.a"
OPENCV_IMGCODECS_LIB="$OPENCV_STATIC_LIB_DIR/libopencv_imgcodecs4130.a"

if [ ! -f "$OPENCV_CORE_LIB" ]; then
    echo "❌ 未找到核心库: $OPENCV_CORE_LIB"
    exit 1
fi

if [ ! -f "$OPENCV_IMGPROC_LIB" ]; then
    echo "❌ 未找到图像处理库: $OPENCV_IMGPROC_LIB"
    exit 1
fi

if [ ! -f "$OPENCV_IMGCODECS_LIB" ]; then
    echo "❌ 未找到图像编解码库: $OPENCV_IMGCODECS_LIB"
    exit 1
fi

echo "✓ 找到OpenCV核心库: $(ls -lh "$OPENCV_CORE_LIB" | awk '{print $5}')"
echo "✓ 找到图像处理库: $(ls -lh "$OPENCV_IMGPROC_LIB" | awk '{print $5}')"
echo "✓ 找到图像编解码库: $(ls -lh "$OPENCV_IMGCODECS_LIB" | awk '{print $5}')"

# 检查头文件
if [ ! -d "$OPENCV_STATIC_INCLUDE_DIR" ]; then
    echo "❌ 未找到头文件目录: $OPENCV_STATIC_INCLUDE_DIR"
    exit 1
fi

echo "✓ 找到头文件目录: $OPENCV_STATIC_INCLUDE_DIR"

# 创建构建目录
echo "========================================"
echo "准备构建环境"
echo "========================================"

BUILD_DIR="build_user_opencv"
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo "✓ 创建构建目录: $BUILD_DIR"

# 设置编译器和标志
CC="/mingw64/bin/gcc.exe"
CXX="/mingw64/bin/g++.exe"

# 编译标志
CFLAGS="-O2 -DUSE_OPENCV -I$OPENCV_STATIC_INCLUDE_DIR"
CXXFLAGS="-O2 -std=c++17 -DUSE_OPENCV -I$OPENCV_STATIC_INCLUDE_DIR"
LDFLAGS="-static -static-libgcc -static-libstdc++"

echo "✓ 编译器: $CC, $CXX"
echo "✓ 编译标志: $CXXFLAGS"

# 查找源文件
echo "========================================"
echo "查找源文件"
echo "========================================"

SOURCE_FILES=(
    "../src/dataset_export.cpp"
    "../src/image_processor.cpp"
    "../src/format_converter.cpp"
    "../src/file_utils.cpp"
)

FOUND_SOURCES=()
for src in "${SOURCE_FILES[@]}"; do
    if [ -f "$src" ]; then
        FOUND_SOURCES+=("$src")
        echo "✓ 找到源文件: $src"
    else
        echo "⚠️  未找到源文件: $src"
    fi
done

if [ ${#FOUND_SOURCES[@]} -eq 0 ]; then
    echo "❌ 未找到任何源文件"
    exit 1
fi

echo "✓ 找到 ${#FOUND_SOURCES[@]} 个源文件"

# 查找OpenCV依赖的静态库
echo "========================================"
echo "查找OpenCV依赖库"
echo "========================================"

# OpenCV静态库通常需要的第三方库
OPENCV_DEPS=(
    "liblibjpeg-turbo.a"
    "liblibpng.a"
    "liblibtiff.a"
    "liblibwebp.a"
    "libzlib.a"
)

FOUND_DEPS=()
for dep in "${OPENCV_DEPS[@]}"; do
    dep_path="$OPENCV_STATIC_LIB_DIR/$dep"
    if [ -f "$dep_path" ]; then
        FOUND_DEPS+=("$dep_path")
        echo "✓ 找到依赖库: $dep"
    else
        echo "⚠️  未找到依赖库: $dep"
    fi
done

echo "✓ 找到 ${#FOUND_DEPS[@]} 个依赖库"

# 开始编译
echo "========================================"
echo "开始编译"
echo "========================================"

# 编译命令
COMPILE_CMD=(
    "$CXX"
    $CXXFLAGS
    $LDFLAGS
    -shared
    -o "dataset_export.dll"
    "${FOUND_SOURCES[@]}"
    "$OPENCV_CORE_LIB"
    "$OPENCV_IMGPROC_LIB"
    "$OPENCV_IMGCODECS_LIB"
    "${FOUND_DEPS[@]}"
    -lole32 -loleaut32 -luuid -lgdi32 -luser32
    -lkernel32 -ladvapi32 -lws2_32 -lwsock32
)

echo "编译命令:"
echo "${COMPILE_CMD[@]}"
echo ""

echo "开始编译..."
"${COMPILE_CMD[@]}"

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    echo ""
    echo "可能的原因："
    echo "1. 缺少必要的依赖库"
    echo "2. 静态库版本不兼容"
    echo "3. 链接顺序问题"
    exit 1
fi

echo "✅ 编译成功！"

# 检查生成的DLL
echo "========================================"
echo "检查编译结果"
echo "========================================"

if [ ! -f "dataset_export.dll" ]; then
    echo "❌ 未找到生成的DLL文件"
    exit 1
fi

echo "✓ 找到生成的DLL: dataset_export.dll"
echo "文件大小: $(ls -lh dataset_export.dll | awk '{print $5}')"

# 检查依赖关系
echo ""
echo "检查DLL依赖关系:"
if command -v ldd >/dev/null 2>&1; then
    echo "所有依赖："
    ldd dataset_export.dll | head -20
    
    echo ""
    echo "非系统依赖："
    NON_SYSTEM_DEPS=$(ldd dataset_export.dll | grep -v -E "^.*DLL Name: (KERNEL32|USER32|ADVAPI32|msvcrt|WS2_32|WINMM|CRYPT32|bcrypt|Secur32|IPHLPAPI|WSOCK32|GLU32|dxcore|IMM32|CRYPTBASE|bcryptPrimitives|ntdll|KERNELBASE|sechost|RPCRT4|ucrtbase|win32u|GDI32|gdi32full|msvcp_win|combase|SSPICLI|OPENGL32)\.dll$" | grep -E "(not found|mingw64)")
    
    if [ -n "$NON_SYSTEM_DEPS" ]; then
        echo "$NON_SYSTEM_DEPS"
        echo ""
        echo "⚠️  仍然有非系统依赖！"
    else
        echo "无"
        echo ""
        echo "🎉 恭喜！这是真正的完全静态编译！"
        echo "只依赖Windows系统库，可以直接复制到其他机器使用。"
    fi
else
    echo "ldd命令不可用，使用objdump检查..."
    if command -v objdump >/dev/null 2>&1; then
        objdump -p dataset_export.dll | grep "DLL Name:" | sort
    fi
fi

# 复制到上级目录
echo ""
echo "复制DLL到项目目录..."
cp dataset_export.dll "../lib/"
echo "✓ 已复制到 ../lib/ 目录"

# 显示总结
echo ""
echo "========================================"
echo "编译完成总结"
echo "========================================"
echo "✅ 使用用户编译的静态OpenCV编译成功！"
echo "📁 DLL位置: $(pwd)/dataset_export.dll"
echo "📁 备份位置: ../lib/dataset_export.dll"
echo "🔧 使用的OpenCV: $OPENCV_STATIC_ROOT"
echo "💾 文件大小: $(ls -lh dataset_export.dll | awk '{print $5}')"

echo ""
echo "下一步："
echo "1. 运行 ../auto-deploy-dll.sh 部署到Java项目"
echo "2. 或者运行 ../check-dependencies.sh 检查依赖关系"
echo "3. 测试DLL功能"

echo ""
echo "🎉 静态OpenCV编译完成！"
