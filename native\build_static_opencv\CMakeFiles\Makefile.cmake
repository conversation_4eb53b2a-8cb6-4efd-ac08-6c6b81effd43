# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVConfig-version.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVConfig.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVModules-release.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVModules.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompiler.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerABI.cpp"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDependentOption.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeFindJavaCommon.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakePushCheckState.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeRCCompiler.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystem.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestRCCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CheckSourceCompiles.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Diab-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Renesas-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindPackageMessage.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeInspectCXXLinker.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Determine-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake"
  "C:/work/code/java/data-annotation-platform/native/CMakeLists.txt"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/dataset_export.dir/DependInfo.cmake"
  )
