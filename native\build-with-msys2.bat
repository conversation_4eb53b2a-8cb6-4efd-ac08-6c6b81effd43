@echo off
echo ========================================
echo Building with MSYS2 MinGW64
echo ========================================

REM 设置MSYS2路径
set MSYS2_ROOT=C:\msys64

REM 检查MSYS2是否存在
if not exist "%MSYS2_ROOT%" (
    echo Error: MSYS2 not found at %MSYS2_ROOT%
    pause
    exit /b 1
)

REM 设置vcpkg路径
set VCPKG_ROOT=C:\msys64\home\%USERNAME%\vcpkg

echo Using MSYS2 at: %MSYS2_ROOT%
echo Using vcpkg at: %VCPKG_ROOT%

REM 转换当前目录到MSYS2格式
set CURRENT_DIR=%CD%
set MSYS2_PATH=%CURRENT_DIR:\=/%
set MSYS2_PATH=%MSYS2_PATH:C:=/c%

echo Current directory in MSYS2 format: %MSYS2_PATH%

REM 创建临时构建脚本
echo #!/bin/bash > temp_build.sh
echo export VCPKG_ROOT="/c/msys64/home/<USER>/vcpkg" >> temp_build.sh
echo export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake" >> temp_build.sh
echo export VCPKG_TARGET_TRIPLET="x64-mingw-dynamic" >> temp_build.sh
echo echo "VCPKG_ROOT: $VCPKG_ROOT" >> temp_build.sh
echo echo "Building in directory: $(pwd)" >> temp_build.sh
echo mkdir -p build >> temp_build.sh
echo cd build >> temp_build.sh
echo rm -f CMakeCache.txt >> temp_build.sh
echo rm -rf CMakeFiles >> temp_build.sh
echo echo "Configuring with CMake..." >> temp_build.sh
echo cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE" -DVCPKG_TARGET_TRIPLET="$VCPKG_TARGET_TRIPLET" >> temp_build.sh
echo if [ $? -ne 0 ]; then echo "CMake failed"; exit 1; fi >> temp_build.sh
echo echo "Building..." >> temp_build.sh
echo make -j4 >> temp_build.sh
echo if [ $? -ne 0 ]; then echo "Build failed"; exit 1; fi >> temp_build.sh
echo echo "Build completed!" >> temp_build.sh
echo ls -la bin/ ^|^| ls -la . >> temp_build.sh

REM 启动MSYS2并运行构建
echo Starting MSYS2 MinGW64 environment...
"%MSYS2_ROOT%\msys2_shell.cmd" -mingw64 -defterm -here -no-start -c "cd '%MSYS2_PATH%' && chmod +x temp_build.sh && ./temp_build.sh"

echo.
echo Build process completed.
echo Check the build directory for the generated library files.

REM 清理临时文件
if exist temp_build.sh del temp_build.sh

pause
