cmake_minimum_required(VERSION 3.16)
project(dataset_export CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# ----------------- 1. 静态构建开关 -----------------
option(BUILD_STATIC "Build with static linking" ON)


# ----------------- 2. 查找并配置依赖 -----------------

# 将MinGW环境的路径加入搜索列表，这样find_package才能找到所有依赖
if(MINGW)
    list(APPEND CMAKE_PREFIX_PATH "$ENV{MINGW_PREFIX}")
    message(STATUS "Using MINGW_PREFIX for dependency search: $ENV{MINGW_PREFIX}")
endif()

# 查找 JNI
# TODO: 请根据您的环境修改此路径
set(JAVA_HOME "C:/java/jdk-21.0.2") 
find_package(JNI REQUIRED)
if(JNI_FOUND)
    message(STATUS "✓ Found JNI: headers at ${JNI_INCLUDE_DIRS}")
endif()

# 查找 OpenCV
if(BUILD_STATIC)
    message(STATUS "Looking for STATIC OpenCV...")
    # 强制CMake寻找静态库
    set(OpenCV_STATIC ON)
    
    # TODO: 请确认此路径指向您手动编译的OpenCV目录下，含有OpenCVConfig.cmake文件的目录
    set(OpenCV_DIR "C:/dev/opencv_static/x64/mingw")
    
    find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs)
else()
    message(STATUS "Looking for DYNAMIC OpenCV...")
    find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs)
endif()

# 检查并打印OpenCV查找结果
if(OpenCV_FOUND)
    message(STATUS "✓ Found OpenCV ${OpenCV_VERSION}")
    message(STATUS "  Includes: ${OpenCV_INCLUDE_DIRS}")
    # ${OpenCV_LIBS} 变量现在包含了所有需要的一切，并且顺序正确
    # 取消下面一行的注释，可以查看CMake为你找到了哪些库
    # message(STATUS "  Libraries to link: ${OpenCV_LIBS}")
endif()


# ----------------- 3. 构建目标 -----------------

# 包含目录
include_directories(
    src
    include
    ${JNI_INCLUDE_DIRS}
    ${OpenCV_INCLUDE_DIRS}
)

# 源文件
set(SOURCES
    src/dataset_export_jni.cpp
    src/coco_exporter.cpp
    src/voc_exporter.cpp
    src/yolo_exporter.cpp
    src/image_processor.cpp
    src/image_crop.cpp
    src/minio_client.cpp
    src/utils.cpp
)

# 创建库目标
add_library(dataset_export SHARED ${SOURCES})


# ----------------- 4. 链接库 -----------------
message(STATUS "Configuring library linking...")

# 链接OpenCV及其所有依赖（这是最关键的一步）
target_link_libraries(dataset_export PRIVATE ${OpenCV_LIBS})

# 链接JNI
target_link_libraries(dataset_export PRIVATE ${JNI_LIBRARIES})


# ----------------- 5. 设置最终的链接器标志 -----------------
if(MINGW AND BUILD_STATIC)
    message(STATUS "✓ Applying final static linking flags for MinGW...")
    # -static 会尝试静态链接所有能找到的库, 包括libgcc, libstdc++, libwinpthread
    # 将它单独加在最后，作用于整个链接过程
    target_link_libraries(dataset_export PRIVATE -static)

    # 有些Windows基础库即使在-static下也最好显式链接
    target_link_libraries(dataset_export PRIVATE ws2_32 gdi32 opengl32 comdlg32)
endif()


# ----------------- 6. 输出和安装 -----------------
set_target_properties(dataset_export PROPERTIES
    OUTPUT_NAME "dataset_export"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin" # 直接输出到bin目录
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 安装规则
install(TARGETS dataset_export
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# 构建后自动复制DLL到Java项目
# TODO: 请确认此路径是您Java项目的资源路径
if(EXISTS "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native")
    add_custom_command(TARGET dataset_export POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:dataset_export>
        "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native/"
        COMMENT "Copying library to Java resources directory"
    )
endif()

message(STATUS "--- Build configuration finished ---")

