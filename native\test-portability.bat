@echo off
REM Windows批处理脚本，用于测试DLL的便携性

echo ========================================
echo 测试DLL便携性
echo ========================================

REM 查找DLL文件
set DLL_FILE=
if exist "build\bin\dataset_export.dll" set DLL_FILE=build\bin\dataset_export.dll
if exist "build\lib\dataset_export.dll" set DLL_FILE=build\lib\dataset_export.dll
if exist "lib\dataset_export.dll" set DLL_FILE=lib\dataset_export.dll

if "%DLL_FILE%"=="" (
    echo ❌ 未找到dataset_export.dll文件
    echo 请先编译项目
    pause
    exit /b 1
)

echo ✓ 找到DLL文件: %DLL_FILE%
echo.

REM 显示文件大小
for %%A in ("%DLL_FILE%") do echo 文件大小: %%~zA 字节

echo.
echo ========================================
echo 使用dumpbin检查依赖关系 (如果有Visual Studio)
echo ========================================

where dumpbin >nul 2>&1
if %errorlevel%==0 (
    echo 使用dumpbin检查依赖:
    dumpbin /dependents "%DLL_FILE%"
) else (
    echo dumpbin不可用，跳过此检查
)

echo.
echo ========================================
echo 便携性测试建议
echo ========================================
echo 1. 将 %DLL_FILE% 复制到一个干净的Windows机器上
echo 2. 确保目标机器没有安装MinGW、MSYS2或Visual Studio
echo 3. 创建一个简单的Java程序来加载这个DLL
echo 4. 如果能正常加载和调用，说明是真正的静态编译
echo.
echo 如果出现 "找不到指定的模块" 错误，说明还有动态依赖
echo.

echo ========================================
echo 创建便携性测试包
echo ========================================

set TEST_DIR=portability_test
if exist "%TEST_DIR%" rmdir /s /q "%TEST_DIR%"
mkdir "%TEST_DIR%"

copy "%DLL_FILE%" "%TEST_DIR%\"
echo ✓ 已复制DLL到 %TEST_DIR% 目录

REM 创建测试Java文件
echo 创建测试Java文件...
(
echo public class TestDLL {
echo     static {
echo         try {
echo             System.loadLibrary("dataset_export"^);
echo             System.out.println("✓ DLL加载成功！"^);
echo         } catch (UnsatisfiedLinkError e^) {
echo             System.err.println("❌ DLL加载失败: " + e.getMessage(^)^);
echo             e.printStackTrace(^);
echo         }
echo     }
echo.
echo     public static void main(String[] args^) {
echo         System.out.println("测试完成"^);
echo     }
echo }
) > "%TEST_DIR%\TestDLL.java"

echo ✓ 已创建测试Java文件: %TEST_DIR%\TestDLL.java

echo.
echo 测试包已创建在 %TEST_DIR% 目录中
echo 您可以将整个目录复制到其他机器上进行测试
echo.

pause
