
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -Aa 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -D__CLASSIC_C__ 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      gcc.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      gcc.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -Aa 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -D__CLASSIC_C__ 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      gcc.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      gcc.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
      gcc.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
      gcc.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/ar.com"
    found: "C:/msys64/mingw64/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/ranlib.com"
    found: "C:/msys64/mingw64/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/strip.com"
    found: "C:/msys64/mingw64/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/ld.com"
    found: "C:/msys64/mingw64/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/nm.com"
    found: "C:/msys64/mingw64/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/objdump.com"
    found: "C:/msys64/mingw64/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/objcopy.com"
    found: "C:/msys64/mingw64/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/readelf.com"
    found: "C:/msys64/mingw64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/dlltool.com"
    found: "C:/msys64/mingw64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/addr2line.com"
    found: "C:/msys64/mingw64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/msys64/opt/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/mingw64/bin/tapi.com"
      - "C:/msys64/mingw64/bin/tapi.exe"
      - "C:/msys64/mingw64/bin/tapi"
      - "C:/msys64/usr/local/bin/tapi.com"
      - "C:/msys64/usr/local/bin/tapi.exe"
      - "C:/msys64/usr/local/bin/tapi"
      - "C:/msys64/usr/bin/tapi.com"
      - "C:/msys64/usr/bin/tapi.exe"
      - "C:/msys64/usr/bin/tapi"
      - "C:/msys64/opt/bin/tapi.com"
      - "C:/msys64/opt/bin/tapi.exe"
      - "C:/msys64/opt/bin/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/msys64/usr/bin/site_perl/tapi.com"
      - "C:/msys64/usr/bin/site_perl/tapi.exe"
      - "C:/msys64/usr/bin/site_perl/tapi"
      - "C:/msys64/usr/bin/vendor_perl/tapi.com"
      - "C:/msys64/usr/bin/vendor_perl/tapi.exe"
      - "C:/msys64/usr/bin/vendor_perl/tapi"
      - "C:/msys64/usr/bin/core_perl/tapi.com"
      - "C:/msys64/usr/bin/core_perl/tapi.exe"
      - "C:/msys64/usr/bin/core_perl/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--c++'
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--ec++'; did you mean '-Weffc++'?
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      g++.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--c++'
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--ec++'; did you mean '-Weffc++'?
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      g++.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\opt\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/msys64/mingw64"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      g++.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      g++.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-6084zy"
      binary: "C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-6084zy"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-6084zy'
        
        Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_77b77/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_77b77.dir\\build.make CMakeFiles/cmTC_77b77.dir/build
        mingw32-make[1]: Entering directory 'C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-6084zy'
        Building C object CMakeFiles/cmTC_77b77.dir/CMakeCCompilerABI.c.obj
        C:\\msys64\\mingw64\\bin\\gcc.exe    -o CMakeFiles\\cmTC_77b77.dir\\CMakeCCompilerABI.c.obj -c C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCCompilerABI.c
        mingw32-make[1]: *** [CMakeFiles\\cmTC_77b77.dir\\build.make:80: CMakeFiles/cmTC_77b77.dir/CMakeCCompilerABI.c.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-6084zy'
        mingw32-make: *** [Makefile:133: cmTC_77b77/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working C compiler: C:/msys64/mingw64/bin/gcc.exe"
    directories:
      source: "C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-aaf93r"
      binary: "C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-aaf93r"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-aaf93r'
        
        Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_f770a/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_f770a.dir\\build.make CMakeFiles/cmTC_f770a.dir/build
        mingw32-make[1]: Entering directory 'C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-aaf93r'
        Building C object CMakeFiles/cmTC_f770a.dir/testCCompiler.c.obj
        C:\\msys64\\mingw64\\bin\\gcc.exe    -o CMakeFiles\\cmTC_f770a.dir\\testCCompiler.c.obj -c C:\\work\\code\\java\\data-annotation-platform\\native\\opencv_static_build\\CMakeFiles\\CMakeScratch\\TryCompile-aaf93r\\testCCompiler.c
        mingw32-make[1]: *** [CMakeFiles\\cmTC_f770a.dir\\build.make:80: CMakeFiles/cmTC_f770a.dir/testCCompiler.c.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/work/code/java/data-annotation-platform/native/opencv_static_build/CMakeFiles/CMakeScratch/TryCompile-aaf93r'
        mingw32-make: *** [Makefile:133: cmTC_f770a/fast] Error 2
        
      exitCode: 2
...
