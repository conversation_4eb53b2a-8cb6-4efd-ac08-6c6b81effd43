#!/bin/bash

# 检查DLL依赖关系的脚本
# 用于验证是否真正实现了静态编译

echo "========================================"
echo "检查DLL依赖关系"
echo "========================================"

# 查找生成的DLL文件
DLL_PATHS=(
    "build/bin/dataset_export.dll"
    "build/lib/dataset_export.dll"
    "lib/dataset_export.dll"
)

DLL_FILE=""
for path in "${DLL_PATHS[@]}"; do
    if [ -f "$path" ]; then
        DLL_FILE="$path"
        echo "✓ 找到DLL文件: $DLL_FILE"
        break
    fi
done

if [ -z "$DLL_FILE" ]; then
    echo "❌ 未找到dataset_export.dll文件"
    echo "请先编译项目"
    exit 1
fi

echo ""
echo "========================================"
echo "文件信息"
echo "========================================"
ls -lh "$DLL_FILE"
echo ""

echo "========================================"
echo "使用objdump检查依赖关系"
echo "========================================"

if command -v objdump >/dev/null 2>&1; then
    echo "DLL依赖的动态库："
    objdump -p "$DLL_FILE" | grep "DLL Name:" | sort
    echo ""
    
    # 检查是否有MinGW运行时依赖
    MINGW_DEPS=$(objdump -p "$DLL_FILE" | grep "DLL Name:" | grep -E "(libgcc|libstdc|libwinpthread)")
    
    if [ -n "$MINGW_DEPS" ]; then
        echo "⚠️  发现MinGW运行时依赖："
        echo "$MINGW_DEPS"
        echo ""
        echo "这意味着不是完全静态编译！"
        echo "需要在目标机器上安装这些DLL或将它们一起分发。"
    else
        echo "✅ 未发现MinGW运行时依赖！"
        echo "这是一个好兆头，可能实现了静态编译。"
    fi
    
    echo ""
    echo "========================================"
    echo "详细依赖分析"
    echo "========================================"
    
    # 分析每个依赖
    objdump -p "$DLL_FILE" | grep "DLL Name:" | while read -r line; do
        dll_name=$(echo "$line" | awk '{print $3}')
        case "$dll_name" in
            "KERNEL32.dll"|"USER32.dll"|"ADVAPI32.dll"|"msvcrt.dll"|"WS2_32.dll"|"WINMM.dll"|"CRYPT32.dll")
                echo "✅ $dll_name - Windows系统库（正常）"
                ;;
            "libgcc_s_seh-1.dll"|"libgcc_s_dw2-1.dll")
                echo "❌ $dll_name - MinGW GCC运行时库（需要静态链接）"
                ;;
            "libstdc++-6.dll")
                echo "❌ $dll_name - MinGW C++标准库（需要静态链接）"
                ;;
            "libwinpthread-1.dll")
                echo "❌ $dll_name - MinGW pthread库（需要静态链接）"
                ;;
            *)
                echo "⚠️  $dll_name - 其他依赖（需要检查）"
                ;;
        esac
    done
    
else
    echo "❌ objdump命令不可用"
    echo "请安装binutils: pacman -S mingw-w64-x86_64-binutils"
fi

echo ""
echo "========================================"
echo "使用ldd检查依赖关系（如果可用）"
echo "========================================"

if command -v ldd >/dev/null 2>&1; then
    echo "ldd输出："
    ldd "$DLL_FILE" 2>/dev/null || echo "ldd无法处理此文件"
else
    echo "ldd命令不可用"
fi

echo ""
echo "========================================"
echo "便携性测试建议"
echo "========================================"
echo "1. 将生成的DLL复制到一个干净的Windows机器上"
echo "2. 确保目标机器没有安装MinGW或MSYS2"
echo "3. 尝试加载DLL，看是否出现缺少DLL的错误"
echo "4. 如果出现错误，说明还有动态依赖需要解决"
echo ""
echo "如果发现MinGW运行时依赖，可以尝试："
echo "1. 在CMakeLists.txt中添加更强的静态链接选项"
echo "2. 使用 -static 链接器标志"
echo "3. 确保所有第三方库都是静态版本"

echo ""
echo "========================================"
echo "检查完成"
echo "========================================"
