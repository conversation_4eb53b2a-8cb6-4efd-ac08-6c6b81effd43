import com.ylzx.annotation.jni.DatasetExportNative;

/**
 * JNI库测试类
 * 用于验证DLL是否正确加载
 */
public class TestJNI {
    public static void main(String[] args) {
        System.out.println("======================================");
        System.out.println("JNI库加载测试");
        System.out.println("======================================");
        
        // 显示系统信息
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println("架构: " + System.getProperty("os.arch"));
        System.out.println("库路径: " + System.getProperty("java.library.path"));
        System.out.println();
        
        // 测试JNI库加载
        try {
            System.out.println("测试JNI库加载...");
            
            // 检查库是否已加载
            boolean loaded = DatasetExportNative.isLibraryLoaded();
            System.out.println("库加载状态: " + (loaded ? "✓ 已加载" : "❌ 未加载"));
            
            if (loaded) {
                // 测试基本功能
                System.out.println("测试基本功能...");
                
                // 获取库版本
                try {
                    DatasetExportNative native = new DatasetExportNative();
                    String version = native.getLibraryVersion();
                    System.out.println("库版本: " + version);
                    
                    // 获取支持的格式
                    String[] formats = native.getSupportedFormats();
                    System.out.println("支持的格式: " + String.join(", ", formats));
                    
                    System.out.println("✅ 所有测试通过！");
                    
                } catch (Exception e) {
                    System.out.println("⚠️  库已加载但功能测试失败: " + e.getMessage());
                }
            } else {
                System.out.println("❌ 库未加载，请检查DLL文件是否正确部署");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("测试完成");
    }
}
