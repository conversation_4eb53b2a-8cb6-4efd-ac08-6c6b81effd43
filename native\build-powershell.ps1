# PowerShell build script for C++ native library
Write-Host "========================================"
Write-Host "Building C++ native library with MinGW"
Write-Host "========================================"

# 设置环境变量
$env:VCPKG_ROOT = "C:\msys64\home\$env:USERNAME\vcpkg"
$env:PATH = "C:\msys64\mingw64\bin;$env:PATH"

Write-Host "VCPKG_ROOT: $env:VCPKG_ROOT"
Write-Host "Using MinGW from: C:\msys64\mingw64\bin"

# 检查工具是否可用
try {
    $cmakeVersion = & cmake --version 2>$null
    Write-Host "✓ CMake found: $($cmakeVersion[0])"
} catch {
    Write-Host "❌ CMake not found in PATH"
    exit 1
}

try {
    $gccVersion = & gcc --version 2>$null
    Write-Host "✓ GCC found: $($gccVersion[0])"
} catch {
    Write-Host "❌ GCC not found in PATH"
    exit 1
}

# 检查vcpkg
if (Test-Path "$env:VCPKG_ROOT\vcpkg.exe") {
    Write-Host "✓ vcpkg found at: $env:VCPKG_ROOT"
} else {
    Write-Host "❌ vcpkg not found at: $env:VCPKG_ROOT"
    exit 1
}

# 创建build目录
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Name "build" | Out-Null
}

# 进入build目录
Push-Location build

try {
    # 清理之前的配置
    if (Test-Path "CMakeCache.txt") {
        Remove-Item "CMakeCache.txt" -Force
    }
    if (Test-Path "CMakeFiles") {
        Remove-Item "CMakeFiles" -Recurse -Force
    }

    Write-Host "========================================"
    Write-Host "Configuring with CMake..."
    Write-Host "========================================"

    # 配置CMake
    $cmakeArgs = @(
        ".."
        "-G", "MinGW Makefiles"
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_TOOLCHAIN_FILE=$env:VCPKG_ROOT\scripts\buildsystems\vcpkg.cmake"
        "-DVCPKG_TARGET_TRIPLET=x64-mingw-dynamic"
        "-DCMAKE_C_COMPILER=gcc"
        "-DCMAKE_CXX_COMPILER=g++"
    )

    & cmake @cmakeArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ CMake configuration failed!"
        exit 1
    }

    Write-Host "========================================"
    Write-Host "Building..."
    Write-Host "========================================"

    # 构建
    & mingw32-make -j4
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed!"
        exit 1
    }

    Write-Host "========================================"
    Write-Host "Build completed successfully! ✓"
    Write-Host "========================================"

    # 检查生成的文件
    $dllFiles = Get-ChildItem -Name "*.dll", "bin\*.dll" -ErrorAction SilentlyContinue
    if ($dllFiles) {
        Write-Host "Generated library files:"
        foreach ($dll in $dllFiles) {
            Write-Host "  ✓ $dll"
        }

        # 复制到Java resources目录
        $javaResourcesDir = "..\..\ylzx-annotation\src\main\resources\native"
        if (Test-Path $javaResourcesDir) {
            Write-Host "Copying to Java resources directory..."
            foreach ($dll in $dllFiles) {
                Copy-Item $dll $javaResourcesDir -Force
                Write-Host "  ✓ Copied $dll to Java resources"
            }
        } else {
            Write-Host "⚠ Java resources directory not found: $javaResourcesDir"
        }
    } else {
        Write-Host "❌ No DLL files found!"
    }

} finally {
    # 返回原目录
    Pop-Location
}

Write-Host ""
Write-Host "Build process completed!"
Write-Host "Check the build/bin directory for the generated library files."
