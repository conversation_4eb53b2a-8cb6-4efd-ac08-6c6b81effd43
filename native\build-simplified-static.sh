#!/bin/bash

# 使用简化后的CMakeLists.txt进行静态编译
# 相信CMake的自动化机制，不手动查找依赖库

echo "========================================"
echo "使用简化的CMake配置进行静态编译"
echo "========================================"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ 请在MSYS2环境中运行此脚本"
    exit 1
fi

echo "✓ MSYS2环境: $MSYSTEM"

# 用户编译的静态OpenCV路径
OPENCV_STATIC_ROOT="C:/dev/opencv_static"

echo "========================================"
echo "验证静态OpenCV库"
echo "========================================"

if [ ! -d "$OPENCV_STATIC_ROOT" ]; then
    echo "❌ 未找到静态OpenCV目录: $OPENCV_STATIC_ROOT"
    echo "请确保您已经编译了静态版本的OpenCV"
    exit 1
fi

echo "✓ 找到静态OpenCV目录: $OPENCV_STATIC_ROOT"

# 清理构建目录
echo "========================================"
echo "清理构建环境"
echo "========================================"

BUILD_DIR="build"
echo "清理构建目录: $BUILD_DIR"
rm -rf "$BUILD_DIR"/*
mkdir -p "$BUILD_DIR"

echo "✓ 构建目录已清理"

# 设置环境变量
export CC=/mingw64/bin/gcc.exe
export CXX=/mingw64/bin/g++.exe

echo "✓ 设置编译器: $CC, $CXX"

# CMake配置参数
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC=ON
    -DCMAKE_C_COMPILER="$CC"
    -DCMAKE_CXX_COMPILER="$CXX"
    -DCMAKE_MAKE_PROGRAM=/mingw64/bin/mingw32-make.exe
    
    # 强制静态链接
    -DCMAKE_FIND_LIBRARY_SUFFIXES=".a"
    -DBUILD_SHARED_LIBS=OFF
    
    # 指定静态OpenCV路径
    -DOPENCV_STATIC_ROOT="$OPENCV_STATIC_ROOT"
)

# 运行CMake配置
echo "========================================"
echo "运行CMake配置"
echo "========================================"

echo "CMake参数:"
for arg in "${CMAKE_ARGS[@]}"; do
    echo "  $arg"
done

echo ""
echo "开始配置..."

cd "$BUILD_DIR"
/mingw64/bin/cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败！"
    echo ""
    echo "可能的原因："
    echo "1. 静态OpenCV库路径不正确"
    echo "2. 缺少必要的依赖库"
    echo "3. 编译器配置问题"
    echo ""
    echo "请检查上面的错误信息"
    exit 1
fi

echo "✅ CMake配置成功！"

# 开始编译
echo "========================================"
echo "开始编译"
echo "========================================"

echo "使用 $(nproc) 个并行任务进行编译..."

/mingw64/bin/mingw32-make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    echo ""
    echo "可能的原因："
    echo "1. 静态库链接错误"
    echo "2. 缺少依赖库"
    echo "3. 链接顺序问题"
    echo ""
    echo "请检查上面的错误信息"
    exit 1
fi

echo "✅ 编译成功！"

# 检查生成的DLL
echo "========================================"
echo "检查编译结果"
echo "========================================"

# 查找生成的DLL
DLL_PATHS=("bin/dataset_export.dll" "lib/dataset_export.dll")
DLL_FILE=""

for path in "${DLL_PATHS[@]}"; do
    if [ -f "$path" ]; then
        DLL_FILE="$path"
        break
    fi
done

if [ -z "$DLL_FILE" ]; then
    echo "❌ 未找到生成的DLL文件"
    echo "可用文件："
    find . -name "*.dll" -o -name "*.so" | head -10
    exit 1
fi

echo "✓ 找到生成的DLL: $DLL_FILE"
echo "文件大小: $(ls -lh "$DLL_FILE" | awk '{print $5}')"

# 检查依赖关系
echo ""
echo "检查DLL依赖关系:"
if command -v ldd >/dev/null 2>&1; then
    echo "所有依赖："
    ldd "$DLL_FILE" | head -20
    
    echo ""
    echo "OpenCV相关依赖："
    OPENCV_DEPS_CHECK=$(ldd "$DLL_FILE" | grep -i opencv || echo "无")
    if [ "$OPENCV_DEPS_CHECK" = "无" ]; then
        echo "🎉 太好了！没有发现OpenCV动态库依赖！"
        echo "这意味着OpenCV已经静态链接到DLL中了！"
    else
        echo "❌ 仍然发现OpenCV动态库依赖："
        echo "$OPENCV_DEPS_CHECK"
    fi
    
    echo ""
    echo "MinGW运行时依赖："
    MINGW_DEPS=$(ldd "$DLL_FILE" | grep -E "(libgcc|libstdc|libwinpthread)" || echo "无")
    if [ "$MINGW_DEPS" = "无" ]; then
        echo "🎉 太好了！没有发现MinGW运行时依赖！"
    else
        echo "⚠️  发现MinGW运行时依赖："
        echo "$MINGW_DEPS"
    fi
else
    echo "ldd命令不可用，使用objdump检查..."
    if command -v objdump >/dev/null 2>&1; then
        echo "DLL依赖："
        objdump -p "$DLL_FILE" | grep "DLL Name:" | sort
        
        echo ""
        echo "检查OpenCV依赖："
        OPENCV_CHECK=$(objdump -p "$DLL_FILE" | grep "DLL Name:" | grep -i opencv || echo "无")
        if [ "$OPENCV_CHECK" = "无" ]; then
            echo "🎉 太好了！没有发现OpenCV动态库依赖！"
        else
            echo "❌ 仍然发现OpenCV动态库依赖："
            echo "$OPENCV_CHECK"
        fi
    fi
fi

# 返回上级目录并复制DLL
cd ..
echo ""
echo "复制DLL到项目目录..."
cp "$BUILD_DIR/$DLL_FILE" "lib/"
cp "$BUILD_DIR/$DLL_FILE" "build/bin/"
echo "✓ 已复制到 lib/ 和 build/bin/ 目录"

# 显示总结
echo ""
echo "========================================"
echo "编译完成总结"
echo "========================================"
echo "✅ 使用简化的CMake配置编译成功！"
echo "📁 DLL位置: $BUILD_DIR/$DLL_FILE"
echo "📁 备份位置: lib/$(basename "$DLL_FILE")"
echo "📁 构建位置: build/bin/$(basename "$DLL_FILE")"
echo "🔧 使用的OpenCV: $OPENCV_STATIC_ROOT"
echo "💾 文件大小: $(ls -lh "$BUILD_DIR/$DLL_FILE" | awk '{print $5}')"

echo ""
echo "下一步："
echo "1. 运行 ./check-dependencies.sh 检查依赖关系"
echo "2. 运行 ./auto-deploy-dll.sh 部署到Java项目"
echo "3. 测试DLL功能"

echo ""
echo "🎉 简化的静态编译完成！"
