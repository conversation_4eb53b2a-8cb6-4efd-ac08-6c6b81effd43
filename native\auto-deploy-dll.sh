#!/bin/bash

# 自动部署DLL到Java项目脚本
# 这个脚本会扫描Java项目中JNI库的加载位置，并在编译完成后自动复制DLL到指定位置

echo "========================================"
echo "自动部署DLL到Java项目"
echo "========================================"

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
JAVA_MODULE="ylzx-annotation"
JAVA_RESOURCES_DIR="$PROJECT_ROOT/$JAVA_MODULE/src/main/resources"
NATIVE_RESOURCES_DIR="$JAVA_RESOURCES_DIR/native"

# DLL搜索路径
DLL_SEARCH_PATHS=(
    "$SCRIPT_DIR/build/bin"
    "$SCRIPT_DIR/build/lib" 
    "$SCRIPT_DIR/build"
    "$SCRIPT_DIR/lib"
    "$SCRIPT_DIR/deployment_package"
    "$SCRIPT_DIR/portable_package"
    "$SCRIPT_DIR"
)

# 支持的DLL文件名
DLL_NAMES=(
    "dataset_export.dll"
    "libdataset_export.dll"
    "libdataset_export.so"
    "libdataset_export.dylib"
)

# 检测系统架构
detect_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64|amd64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "aarch64"
            ;;
        i386|i686)
            echo "x86"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 检测操作系统
detect_os() {
    local os=$(uname -s | tr '[:upper:]' '[:lower:]')
    case $os in
        linux*)
            echo "linux"
            ;;
        darwin*)
            echo "macos"
            ;;
        cygwin*|mingw*|msys*)
            echo "windows"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 扫描Java代码中的JNI加载路径
scan_jni_paths() {
    echo "========================================"
    echo "扫描Java项目中的JNI加载路径"
    echo "========================================"
    
    local java_file="$PROJECT_ROOT/$JAVA_MODULE/src/main/java/com/ylzx/annotation/jni/DatasetExportNative.java"
    
    if [ ! -f "$java_file" ]; then
        echo "❌ 未找到DatasetExportNative.java文件: $java_file"
        return 1
    fi
    
    echo "✓ 找到JNI类文件: $java_file"
    echo ""
    echo "分析JNI库加载逻辑:"
    
    # 提取资源路径信息
    echo "1. 通用资源路径:"
    grep -n "resourcePath.*=.*native" "$java_file" | head -3
    
    echo ""
    echo "2. 架构特定路径:"
    grep -n "archSpecificPath.*=.*native" "$java_file" | head -3
    
    echo ""
    echo "3. 库文件名模式:"
    grep -n "libraryName.*=" "$java_file" | head -5
    
    echo ""
    echo "根据代码分析，JNI库加载顺序为:"
    echo "  1. 优先: /native/{架构}/dataset_export.dll"
    echo "  2. 备用: /native/dataset_export.dll"
    echo ""
}

# 查找编译好的DLL文件
find_compiled_dll() {
    echo "========================================"
    echo "查找编译好的DLL文件"
    echo "========================================"
    
    local found_dlls=()
    
    for search_path in "${DLL_SEARCH_PATHS[@]}"; do
        if [ -d "$search_path" ]; then
            echo "搜索目录: $search_path"
            for dll_name in "${DLL_NAMES[@]}"; do
                local dll_path="$search_path/$dll_name"
                if [ -f "$dll_path" ]; then
                    found_dlls+=("$dll_path")
                    echo "  ✓ 找到: $dll_name ($(ls -lh "$dll_path" | awk '{print $5}'))"
                fi
            done
        fi
    done
    
    if [ ${#found_dlls[@]} -eq 0 ]; then
        echo "❌ 未找到任何编译好的DLL文件"
        echo ""
        echo "请先编译项目，或检查以下目录:"
        for path in "${DLL_SEARCH_PATHS[@]}"; do
            echo "  - $path"
        done
        return 1
    fi
    
    echo ""
    echo "找到 ${#found_dlls[@]} 个DLL文件"
    
    # 选择最新的DLL文件
    MAIN_DLL=""
    NEWEST_TIME=0
    
    for dll in "${found_dlls[@]}"; do
        if [[ "$dll" == *"dataset_export.dll" ]]; then
            local file_time=$(stat -c %Y "$dll" 2>/dev/null || stat -f %m "$dll" 2>/dev/null || echo 0)
            if [ "$file_time" -gt "$NEWEST_TIME" ]; then
                NEWEST_TIME=$file_time
                MAIN_DLL="$dll"
            fi
        fi
    done
    
    if [ -z "$MAIN_DLL" ]; then
        MAIN_DLL="${found_dlls[0]}"
    fi
    
    echo "选择主DLL: $MAIN_DLL"
    return 0
}

# 创建Java资源目录结构
create_resources_structure() {
    echo "========================================"
    echo "创建Java资源目录结构"
    echo "========================================"
    
    # 创建基础native目录
    if [ ! -d "$NATIVE_RESOURCES_DIR" ]; then
        mkdir -p "$NATIVE_RESOURCES_DIR"
        echo "✓ 创建目录: $NATIVE_RESOURCES_DIR"
    else
        echo "✓ 目录已存在: $NATIVE_RESOURCES_DIR"
    fi
    
    # 创建架构特定目录
    local arch=$(detect_architecture)
    local arch_dir="$NATIVE_RESOURCES_DIR/$arch"
    
    if [ ! -d "$arch_dir" ]; then
        mkdir -p "$arch_dir"
        echo "✓ 创建架构目录: $arch_dir"
    else
        echo "✓ 架构目录已存在: $arch_dir"
    fi
    
    echo "✓ 资源目录结构已准备就绪"
}

# 复制DLL文件到Java项目
deploy_dll_to_java() {
    echo "========================================"
    echo "部署DLL到Java项目"
    echo "========================================"
    
    if [ -z "$MAIN_DLL" ]; then
        echo "❌ 没有找到要部署的DLL文件"
        return 1
    fi
    
    local dll_name=$(basename "$MAIN_DLL")
    local arch=$(detect_architecture)
    local os=$(detect_os)
    
    echo "部署信息:"
    echo "  源文件: $MAIN_DLL"
    echo "  目标模块: $JAVA_MODULE"
    echo "  操作系统: $os"
    echo "  架构: $arch"
    echo ""
    
    # 1. 复制到通用位置 /native/dataset_export.dll
    local generic_target="$NATIVE_RESOURCES_DIR/dataset_export.dll"
    echo "1. 复制到通用位置:"
    echo "   $generic_target"
    
    cp "$MAIN_DLL" "$generic_target"
    if [ $? -eq 0 ]; then
        echo "   ✓ 复制成功"
    else
        echo "   ❌ 复制失败"
        return 1
    fi
    
    # 2. 复制到架构特定位置 /native/{arch}/dataset_export.dll
    local arch_target="$NATIVE_RESOURCES_DIR/$arch/dataset_export.dll"
    echo ""
    echo "2. 复制到架构特定位置:"
    echo "   $arch_target"
    
    cp "$MAIN_DLL" "$arch_target"
    if [ $? -eq 0 ]; then
        echo "   ✓ 复制成功"
    else
        echo "   ❌ 复制失败"
        return 1
    fi
    
    # 3. 如果有依赖DLL，也一起复制
    local dll_dir=$(dirname "$MAIN_DLL")
    local dependency_dlls=(
        "libopencv_core-412.dll"
        "libopencv_imgproc-412.dll" 
        "libopencv_imgcodecs-412.dll"
    )
    
    echo ""
    echo "3. 检查并复制依赖DLL:"
    
    local deps_copied=0
    for dep_dll in "${dependency_dlls[@]}"; do
        local dep_path="$dll_dir/$dep_dll"
        if [ -f "$dep_path" ]; then
            echo "   复制依赖: $dep_dll"
            cp "$dep_path" "$NATIVE_RESOURCES_DIR/"
            cp "$dep_path" "$NATIVE_RESOURCES_DIR/$arch/"
            ((deps_copied++))
        fi
    done
    
    if [ $deps_copied -gt 0 ]; then
        echo "   ✓ 复制了 $deps_copied 个依赖DLL"
    else
        echo "   ⚠️  未找到依赖DLL（可能已静态链接）"
    fi
    
    return 0
}

# 验证部署结果
verify_deployment() {
    echo "========================================"
    echo "验证部署结果"
    echo "========================================"
    
    local arch=$(detect_architecture)
    
    echo "检查部署的文件:"
    echo ""
    
    # 检查通用文件
    local generic_dll="$NATIVE_RESOURCES_DIR/dataset_export.dll"
    if [ -f "$generic_dll" ]; then
        echo "✓ 通用DLL: $generic_dll"
        echo "  大小: $(ls -lh "$generic_dll" | awk '{print $5}')"
        echo "  修改时间: $(ls -l "$generic_dll" | awk '{print $6, $7, $8}')"
    else
        echo "❌ 通用DLL不存在: $generic_dll"
    fi
    
    echo ""
    
    # 检查架构特定文件
    local arch_dll="$NATIVE_RESOURCES_DIR/$arch/dataset_export.dll"
    if [ -f "$arch_dll" ]; then
        echo "✓ 架构DLL: $arch_dll"
        echo "  大小: $(ls -lh "$arch_dll" | awk '{print $5}')"
        echo "  修改时间: $(ls -l "$arch_dll" | awk '{print $6, $7, $8}')"
    else
        echo "❌ 架构DLL不存在: $arch_dll"
    fi
    
    echo ""
    
    # 检查依赖文件
    echo "依赖DLL文件:"
    local dep_count=0
    for dll_file in "$NATIVE_RESOURCES_DIR"/*.dll; do
        if [ -f "$dll_file" ] && [[ "$(basename "$dll_file")" != "dataset_export.dll" ]]; then
            echo "  ✓ $(basename "$dll_file")"
            ((dep_count++))
        fi
    done
    
    if [ $dep_count -eq 0 ]; then
        echo "  无依赖DLL（静态链接）"
    else
        echo "  共 $dep_count 个依赖DLL"
    fi
    
    echo ""
    echo "资源目录结构:"
    if command -v tree >/dev/null 2>&1; then
        tree "$NATIVE_RESOURCES_DIR" -L 2
    else
        find "$NATIVE_RESOURCES_DIR" -type f | sort | sed 's|^|  |'
    fi
}

# 生成Java测试代码
generate_test_code() {
    echo "========================================"
    echo "生成Java测试代码"
    echo "========================================"
    
    local test_file="$NATIVE_RESOURCES_DIR/TestJNI.java"
    
    cat > "$test_file" << 'EOF'
import com.ylzx.annotation.jni.DatasetExportNative;

/**
 * JNI库测试类
 * 用于验证DLL是否正确加载
 */
public class TestJNI {
    public static void main(String[] args) {
        System.out.println("======================================");
        System.out.println("JNI库加载测试");
        System.out.println("======================================");
        
        // 显示系统信息
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println("架构: " + System.getProperty("os.arch"));
        System.out.println("库路径: " + System.getProperty("java.library.path"));
        System.out.println();
        
        // 测试JNI库加载
        try {
            System.out.println("测试JNI库加载...");
            
            // 检查库是否已加载
            boolean loaded = DatasetExportNative.isLibraryLoaded();
            System.out.println("库加载状态: " + (loaded ? "✓ 已加载" : "❌ 未加载"));
            
            if (loaded) {
                // 测试基本功能
                System.out.println("测试基本功能...");
                
                // 获取库版本
                try {
                    DatasetExportNative native = new DatasetExportNative();
                    String version = native.getLibraryVersion();
                    System.out.println("库版本: " + version);
                    
                    // 获取支持的格式
                    String[] formats = native.getSupportedFormats();
                    System.out.println("支持的格式: " + String.join(", ", formats));
                    
                    System.out.println("✅ 所有测试通过！");
                    
                } catch (Exception e) {
                    System.out.println("⚠️  库已加载但功能测试失败: " + e.getMessage());
                }
            } else {
                System.out.println("❌ 库未加载，请检查DLL文件是否正确部署");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("测试完成");
    }
}
EOF
    
    echo "✓ 生成测试文件: $test_file"
    echo ""
    echo "使用方法:"
    echo "1. 在Java项目根目录运行:"
    echo "   javac -cp target/classes $test_file"
    echo "   java -cp target/classes:. TestJNI"
    echo ""
    echo "2. 或者在IDE中直接运行TestJNI类"
}

# 显示使用说明
show_usage_instructions() {
    echo "========================================"
    echo "使用说明"
    echo "========================================"
    
    local arch=$(detect_architecture)
    
    echo "DLL已成功部署到Java项目！"
    echo ""
    echo "Java加载机制:"
    echo "1. DatasetExportNative会优先尝试加载:"
    echo "   /native/$arch/dataset_export.dll"
    echo ""
    echo "2. 如果架构特定版本不存在，会加载:"
    echo "   /native/dataset_export.dll"
    echo ""
    echo "3. 库文件会被复制到临时目录并加载"
    echo ""
    echo "下一步操作:"
    echo "1. 重新编译Java项目: mvn clean compile"
    echo "2. 重启Java应用程序"
    echo "3. 查看日志中的JNI库加载信息"
    echo "4. 运行测试验证功能"
    echo ""
    echo "故障排除:"
    echo "- 如果加载失败，检查DLL依赖是否完整"
    echo "- 确保Java应用有权限访问resources目录"
    echo "- 查看Java应用日志中的详细错误信息"
}

# 主函数
main() {
    echo "开始时间: $(date)"
    echo "脚本位置: $SCRIPT_DIR"
    echo "项目根目录: $PROJECT_ROOT"
    echo "Java模块: $JAVA_MODULE"
    echo ""
    
    # 1. 扫描JNI路径
    scan_jni_paths
    
    # 2. 查找编译好的DLL
    if ! find_compiled_dll; then
        echo ""
        echo "❌ 部署失败：未找到编译好的DLL文件"
        echo "请先运行编译脚本，例如:"
        echo "  ./build-system-libs.sh"
        echo "  ./collect-dependencies.sh"
        exit 1
    fi
    
    # 3. 创建资源目录结构
    create_resources_structure
    
    # 4. 部署DLL到Java项目
    if ! deploy_dll_to_java; then
        echo ""
        echo "❌ 部署失败"
        exit 1
    fi
    
    # 5. 验证部署结果
    verify_deployment
    
    # 6. 生成测试代码
    generate_test_code
    
    # 7. 显示使用说明
    show_usage_instructions
    
    echo ""
    echo "🎉 DLL自动部署完成！"
    echo "完成时间: $(date)"
}

# 运行主函数
main "$@"
