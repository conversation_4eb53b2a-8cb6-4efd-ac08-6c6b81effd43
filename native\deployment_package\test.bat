@echo off
echo ========================================
echo 测试DLL部署包
echo ========================================
echo.

if not exist "dataset_export.dll" (
    echo ❌ 未找到dataset_export.dll
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 找到主DLL文件
echo.

echo 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Java，请安装JDK或JRE
    echo 下载地址: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo ✅ Java环境正常
echo.

echo 编译测试程序...
javac TestDLL.java
if errorlevel 1 (
    echo ❌ Java编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功
echo.

echo 运行DLL测试...
echo ----------------------------------------
java TestDLL
echo ----------------------------------------
echo.

if errorlevel 1 (
    echo ❌ 测试失败，请检查错误信息
) else (
    echo ✅ 测试成功！DLL可以正常使用
)

echo.
echo 测试完成，按任意键退出...
pause >nul
