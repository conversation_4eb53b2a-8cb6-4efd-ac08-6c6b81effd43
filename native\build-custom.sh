#!/bin/bash

echo "========================================"
echo "Custom build for your environment"
echo "========================================"

# 设置环境变量
export VCPKG_ROOT="/c/msys64/home/<USER>/vcpkg"
export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
export VCPKG_TARGET_TRIPLET="x64-mingw-dynamic"

echo "VCPKG_ROOT: $VCPKG_ROOT"
echo "CMAKE_TOOLCHAIN_FILE: $CMAKE_TOOLCHAIN_FILE"
echo "VCPKG_TARGET_TRIPLET: $VCPKG_TARGET_TRIPLET"

# 检查vcpkg是否存在
if [ ! -f "$VCPKG_ROOT/vcpkg.exe" ]; then
    echo "Error: vcpkg.exe not found at $VCPKG_ROOT"
    exit 1
fi

# 检查MinIO是否已安装
echo "Checking MinIO installation..."
if $VCPKG_ROOT/vcpkg.exe list | grep -q "minio-cpp:x64-mingw-dynamic"; then
    echo "✓ MinIO found"
else
    echo "✗ MinIO not found, installing..."
    $VCPKG_ROOT/vcpkg.exe install minio-cpp:x64-mingw-dynamic
fi

# 创建并进入构建目录
mkdir -p build
cd build

# 清理之前的构建
rm -f CMakeCache.txt
rm -rf CMakeFiles

echo "========================================"
echo "Configuring with CMake..."
echo "========================================"

# 配置CMake
cmake .. \
    -G "MinGW Makefiles" \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE" \
    -DVCPKG_TARGET_TRIPLET="$VCPKG_TARGET_TRIPLET" \
    -DCMAKE_C_COMPILER=gcc \
    -DCMAKE_CXX_COMPILER=g++

if [ $? -ne 0 ]; then
    echo "❌ CMake configuration failed!"
    exit 1
fi

echo "========================================"
echo "Building..."
echo "========================================"

# 构建
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "========================================"
echo "Build completed successfully! ✓"
echo "========================================"

# 查找生成的库文件
echo "Looking for generated library files..."
for lib in libdataset_export.dll dataset_export.dll bin/dataset_export.dll; do
    if [ -f "$lib" ]; then
        echo "✓ Found: $lib"
        
        # 复制到Java resources目录
        JAVA_RESOURCES_DIR="../../ylzx-annotation/src/main/resources/native"
        if [ -d "$JAVA_RESOURCES_DIR" ]; then
            echo "Copying to Java resources directory..."
            cp "$lib" "$JAVA_RESOURCES_DIR/"
            if [ $? -eq 0 ]; then
                echo "✓ Library copied to: $JAVA_RESOURCES_DIR/$(basename $lib)"
            else
                echo "❌ Failed to copy library"
            fi
        else
            echo "⚠ Java resources directory not found: $JAVA_RESOURCES_DIR"
            echo "Please copy $(basename $lib) manually to your Java project"
        fi
        break
    fi
done

echo ""
echo "Build completed successfully!"
echo "Library should be available in the build directory and copied to Java resources."
