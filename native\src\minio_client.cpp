#include "minio_client.h"
#include <iostream>
#include <fstream>
#include <sstream>

#ifdef USE_MINIO
#include <miniocpp/client.h>
#include <miniocpp/args.h>
#include <miniocpp/response.h>
#include <miniocpp/error.h>
#endif

namespace MinioIntegration {

// 全局实例管理
std::unique_ptr<MinioClient> MinioManager::instance_ = nullptr;
bool MinioManager::initialized_ = false;

MinioClient::MinioClient() : initialized_(false) {
#ifdef USE_MINIO
    client_ = nullptr;
#endif
}

MinioClient::~MinioClient() {
#ifdef USE_MINIO
    client_.reset();
#endif
}

bool MinioClient::initialize(const MinioConfig& config) {
#ifdef USE_MINIO
    try {
        config_ = config;
        
        // 创建MinIO客户端
        minio::s3::BaseUrl baseUrl(config.endpoint, config.useSSL);
        minio::creds::StaticProvider provider(config.accessKey, config.secretKey);
        
        client_ = std::make_unique<minio::s3::Client>(baseUrl, &provider);
        
        // 测试连接 - 检查存储桶是否存在
        if (!bucketExists(config.bucketName)) {
            // 如果存储桶不存在，尝试创建
            if (!createBucket(config.bucketName)) {
                setError("Failed to create bucket: " + config.bucketName);
                return false;
            }
        }
        
        initialized_ = true;
        clearError();
        return true;
        
    } catch (const std::exception& e) {
        setError("MinIO initialization failed: " + std::string(e.what()));
        return false;
    }
#else
    setError("MinIO support not compiled in");
    return false;
#endif
}

bool MinioClient::bucketExists(const std::string& bucketName) {
#ifdef USE_MINIO
    if (!client_) {
        setError("MinIO client not initialized");
        return false;
    }

    try {
        minio::s3::BucketExistsArgs args;
        args.bucket = bucketName;

        minio::s3::BucketExistsResponse response = client_->BucketExists(args);
        return static_cast<bool>(response);

    } catch (const std::exception& e) {
        setError("Failed to check bucket existence: " + std::string(e.what()));
        return false;
    }
#else
    setError("MinIO support not compiled in");
    return false;
#endif
}

bool MinioClient::createBucket(const std::string& bucketName) {
#ifdef USE_MINIO
    if (!client_) {
        setError("MinIO client not initialized");
        return false;
    }
    
    try {
        minio::s3::MakeBucketArgs args;
        args.bucket = bucketName;
        args.region = config_.region;
        
        minio::s3::MakeBucketResponse response = client_->MakeBucket(args);
        clearError();
        return true;
        
    } catch (const std::exception& e) {
        setError("Failed to create bucket: " + std::string(e.what()));
        return false;
    }
#else
    setError("MinIO support not compiled in");
    return false;
#endif
}

MinioResult MinioClient::downloadToMemory(const std::string& bucketName, 
                                         const std::string& objectName,
                                         std::vector<uint8_t>& data) {
    MinioResult result;
    
#ifdef USE_MINIO
    if (!client_) {
        result.message = "MinIO client not initialized";
        return result;
    }
    
    try {
        minio::s3::GetObjectArgs args;
        args.bucket = bucketName;
        args.object = objectName;
        
        minio::s3::GetObjectResponse response = client_->GetObject(args);

        if (response) {
            // 读取数据到内存
            std::string content = response.data;

            data.assign(content.begin(), content.end());
            result.success = true;
            result.size = data.size();
            result.message = "Download successful";
        } else {
            result.message = "Failed to download object";
        }
        
    } catch (const std::exception& e) {
        result.message = "Download failed: " + std::string(e.what());
    }
#else
    result.message = "MinIO support not compiled in";
#endif
    
    return result;
}

MinioResult MinioClient::uploadFromMemory(const std::string& bucketName,
                                         const std::string& objectName,
                                         const std::vector<uint8_t>& data,
                                         const std::string& contentType) {
    MinioResult result;
    
#ifdef USE_MINIO
    if (!client_) {
        result.message = "MinIO client not initialized";
        return result;
    }
    
    try {
        // 创建字符串流
        std::string dataStr(data.begin(), data.end());
        std::istringstream iss(dataStr);

        minio::s3::PutObjectArgs args(iss, data.size(), -1);
        args.bucket = bucketName;
        args.object = objectName;
        args.content_type = contentType;

        minio::s3::PutObjectResponse response = client_->PutObject(args);
        
        if (response) {
            result.success = true;
            result.etag = response.etag;
            result.size = data.size();
            result.message = "Upload successful";
        } else {
            result.message = "Failed to upload object";
        }
        
    } catch (const std::exception& e) {
        result.message = "Upload failed: " + std::string(e.what());
    }
#else
    result.message = "MinIO support not compiled in";
#endif
    
    return result;
}

MinioResult MinioClient::downloadToFile(const std::string& bucketName,
                                       const std::string& objectName,
                                       const std::string& localPath) {
    MinioResult result;
    
#ifdef USE_MINIO
    if (!client_) {
        result.message = "MinIO client not initialized";
        return result;
    }
    
    try {
        minio::s3::DownloadObjectArgs args;
        args.bucket = bucketName;
        args.object = objectName;
        args.filename = localPath;
        
        minio::s3::DownloadObjectResponse response = client_->DownloadObject(args);
        
        if (response) {
            result.success = true;
            result.message = "Download to file successful";
            
            // 获取文件大小
            std::ifstream file(localPath, std::ios::binary | std::ios::ate);
            if (file.is_open()) {
                result.size = file.tellg();
                file.close();
            }
        } else {
            result.message = "Failed to download object to file";
        }
        
    } catch (const std::exception& e) {
        result.message = "Download to file failed: " + std::string(e.what());
    }
#else
    result.message = "MinIO support not compiled in";
#endif
    
    return result;
}

MinioResult MinioClient::uploadFromFile(const std::string& bucketName,
                                       const std::string& objectName,
                                       const std::string& localPath,
                                       const std::string& contentType) {
    MinioResult result;
    
#ifdef USE_MINIO
    if (!client_) {
        result.message = "MinIO client not initialized";
        return result;
    }
    
    try {
        minio::s3::UploadObjectArgs args;
        args.bucket = bucketName;
        args.object = objectName;
        args.filename = localPath;
        args.content_type = contentType;
        
        minio::s3::UploadObjectResponse response = client_->UploadObject(args);
        
        if (response) {
            result.success = true;
            result.etag = response.etag;
            result.message = "Upload from file successful";
            
            // 获取文件大小
            std::ifstream file(localPath, std::ios::binary | std::ios::ate);
            if (file.is_open()) {
                result.size = file.tellg();
                file.close();
            }
        } else {
            result.message = "Failed to upload object from file";
        }
        
    } catch (const std::exception& e) {
        result.message = "Upload from file failed: " + std::string(e.what());
    }
#else
    result.message = "MinIO support not compiled in";
#endif
    
    return result;
}

MinioResult MinioClient::removeObject(const std::string& bucketName,
                                    const std::string& objectName) {
    MinioResult result;

#ifdef USE_MINIO
    if (!client_) {
        result.message = "MinIO client not initialized";
        return result;
    }

    try {
        minio::s3::RemoveObjectArgs args;
        args.bucket = bucketName;
        args.object = objectName;

        minio::s3::RemoveObjectResponse response = client_->RemoveObject(args);

        result.success = true;
        result.message = "Object removed successfully";

    } catch (const std::exception& e) {
        result.message = "Remove object failed: " + std::string(e.what());
    }
#else
    result.message = "MinIO support not compiled in";
#endif

    return result;
}

MinioResult MinioClient::getObjectInfo(const std::string& bucketName,
                                      const std::string& objectName) {
    MinioResult result;

#ifdef USE_MINIO
    if (!client_) {
        result.message = "MinIO client not initialized";
        return result;
    }

    try {
        minio::s3::StatObjectArgs args;
        args.bucket = bucketName;
        args.object = objectName;

        minio::s3::StatObjectResponse response = client_->StatObject(args);

        if (response) {
            result.success = true;
            result.size = response.size;
            result.etag = response.etag;
            result.message = "Object info retrieved successfully";
        } else {
            result.message = "Failed to get object info";
        }

    } catch (const std::exception& e) {
        result.message = "Get object info failed: " + std::string(e.what());
    }
#else
    result.message = "MinIO support not compiled in";
#endif

    return result;
}

std::string MinioClient::getPresignedUrl(const std::string& bucketName,
                                        const std::string& objectName,
                                        int expiry) {
#ifdef USE_MINIO
    if (!client_) {
        setError("MinIO client not initialized");
        return "";
    }

    try {
        minio::s3::GetPresignedObjectUrlArgs args;
        args.bucket = bucketName;
        args.object = objectName;
        args.expiry_seconds = expiry;

        minio::s3::GetPresignedObjectUrlResponse response = client_->GetPresignedObjectUrl(args);

        if (response) {
            clearError();
            return response.url;
        } else {
            setError("Failed to generate presigned URL");
            return "";
        }

    } catch (const std::exception& e) {
        setError("Generate presigned URL failed: " + std::string(e.what()));
        return "";
    }
#else
    setError("MinIO support not compiled in");
    return "";
#endif
}

void MinioClient::setError(const std::string& error) {
    lastError_ = error;
    std::cerr << "MinIO Error: " << error << std::endl;
}

void MinioClient::clearError() {
    lastError_.clear();
}

// MinioManager实现
MinioClient* MinioManager::getInstance() {
    if (!instance_) {
        instance_ = std::make_unique<MinioClient>();
    }
    return instance_.get();
}

bool MinioManager::initialize(const MinioConfig& config) {
    MinioClient* client = getInstance();
    if (client && client->initialize(config)) {
        initialized_ = true;
        return true;
    }
    return false;
}

void MinioManager::destroy() {
    instance_.reset();
    initialized_ = false;
}

} // namespace MinioIntegration
