#!/bin/bash

echo "========================================"
echo "测试DLL部署包"
echo "========================================"

if [ ! -f "dataset_export.dll" ]; then
    echo "❌ 未找到dataset_export.dll"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

echo "✅ 找到主DLL文件"

echo "检查Java环境..."
if ! command -v java >/dev/null 2>&1; then
    echo "❌ 未找到Java，请安装JDK或JRE"
    exit 1
fi

echo "✅ Java环境正常"

echo "编译测试程序..."
if ! javac TestDLL.java; then
    echo "❌ Java编译失败"
    exit 1
fi

echo "✅ 编译成功"

echo "运行DLL测试..."
echo "----------------------------------------"
java TestDLL
echo "----------------------------------------"

echo "测试完成"
