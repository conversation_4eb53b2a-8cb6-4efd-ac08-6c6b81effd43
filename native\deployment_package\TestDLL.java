public class TestDLL {
    static {
        try {
            // 设置库路径为当前目录
            System.setProperty("java.library.path", ".");
            
            // 加载DLL
            System.loadLibrary("dataset_export");
            System.out.println("✅ DLL加载成功！");
            
            // 如果有JNI方法，可以在这里测试
            // 例如：testMethod();
            
        } catch (UnsatisfiedLinkError e) {
            System.err.println("❌ DLL加载失败: " + e.getMessage());
            System.err.println("请确保所有DLL文件都在同一目录下");
            e.printStackTrace();
        }
    }
    
    public static void main(String[] args) {
        System.out.println("DLL测试完成");
        
        // 显示系统信息
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println("架构: " + System.getProperty("os.arch"));
        System.out.println("库路径: " + System.getProperty("java.library.path"));
    }
}
