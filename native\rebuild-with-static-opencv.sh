#!/bin/bash

# 使用用户编译的静态OpenCV重新编译DLL
# 这个脚本会直接使用您在 C:/dev/opencv_static 编译的静态库

echo "========================================"
echo "使用静态OpenCV重新编译DLL"
echo "========================================"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ 请在MSYS2环境中运行此脚本"
    exit 1
fi

echo "✓ MSYS2环境: $MSYSTEM"

# 用户编译的静态OpenCV路径
OPENCV_STATIC_ROOT="C:/dev/opencv_static"
OPENCV_STATIC_LIB_DIR="$OPENCV_STATIC_ROOT/x64/mingw/staticlib"
OPENCV_STATIC_INCLUDE_DIR="$OPENCV_STATIC_ROOT/include"

echo "========================================"
echo "验证静态OpenCV库"
echo "========================================"

# 检查关键库文件
OPENCV_CORE_LIB="$OPENCV_STATIC_LIB_DIR/libopencv_core4130.a"
OPENCV_IMGPROC_LIB="$OPENCV_STATIC_LIB_DIR/libopencv_imgproc4130.a"
OPENCV_IMGCODECS_LIB="$OPENCV_STATIC_LIB_DIR/libopencv_imgcodecs4130.a"

if [ ! -f "$OPENCV_CORE_LIB" ]; then
    echo "❌ 未找到核心库: $OPENCV_CORE_LIB"
    exit 1
fi

echo "✓ 找到OpenCV核心库: $(ls -lh "$OPENCV_CORE_LIB" | awk '{print $5}')"
echo "✓ 找到图像处理库: $(ls -lh "$OPENCV_IMGPROC_LIB" | awk '{print $5}')"
echo "✓ 找到图像编解码库: $(ls -lh "$OPENCV_IMGCODECS_LIB" | awk '{print $5}')"

# 创建构建目录
echo "========================================"
echo "准备构建环境"
echo "========================================"

BUILD_DIR="build_static_final"
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo "✓ 创建构建目录: $BUILD_DIR"

# 设置编译器和标志
CC="/mingw64/bin/gcc.exe"
CXX="/mingw64/bin/g++.exe"

# 编译标志
CXXFLAGS="-O2 -std=c++17 -DUSE_OPENCV -I$OPENCV_STATIC_INCLUDE_DIR"
LDFLAGS="-static-libgcc -static-libstdc++"

echo "✓ 编译器: $CXX"
echo "✓ 编译标志: $CXXFLAGS"

# 查找源文件
echo "========================================"
echo "查找源文件"
echo "========================================"

# 实际的源文件
SOURCE_FILES=(
    "../src/dataset_export_jni.cpp"
    "../src/image_processor.cpp"
    "../src/coco_exporter.cpp"
    "../src/voc_exporter.cpp"
    "../src/yolo_exporter.cpp"
    "../src/image_crop.cpp"
    "../src/utils.cpp"
)

FOUND_SOURCES=()
for src in "${SOURCE_FILES[@]}"; do
    if [ -f "$src" ]; then
        FOUND_SOURCES+=("$src")
        echo "✓ 找到源文件: $src"
    else
        echo "⚠️  未找到源文件: $src"
    fi
done

if [ ${#FOUND_SOURCES[@]} -eq 0 ]; then
    echo "❌ 未找到任何源文件"
    exit 1
fi

echo "✓ 找到 ${#FOUND_SOURCES[@]} 个源文件"

# 查找OpenCV依赖的静态库
echo "========================================"
echo "查找OpenCV依赖库"
echo "========================================"

# OpenCV静态库通常需要的第三方库
OPENCV_DEPS=(
    "$OPENCV_STATIC_LIB_DIR/liblibjpeg-turbo.a"
    "$OPENCV_STATIC_LIB_DIR/liblibpng.a"
    "$OPENCV_STATIC_LIB_DIR/liblibtiff.a"
    "$OPENCV_STATIC_LIB_DIR/liblibwebp.a"
    "$OPENCV_STATIC_LIB_DIR/libzlib.a"
    "$OPENCV_STATIC_LIB_DIR/liblibopenjp2.a"
    # WebP相关库（解决链接错误）
    "$OPENCV_STATIC_LIB_DIR/libwebp.a"
    "$OPENCV_STATIC_LIB_DIR/libwebpmux.a"
    "$OPENCV_STATIC_LIB_DIR/libwebpdemux.a"
    "$OPENCV_STATIC_LIB_DIR/libsharpyuv.a"
    # OpenEXR相关库
    "$OPENCV_STATIC_LIB_DIR/libIlmImf.a"
    "$OPENCV_STATIC_LIB_DIR/libImath.a"
    "$OPENCV_STATIC_LIB_DIR/libIex.a"
    "$OPENCV_STATIC_LIB_DIR/libIlmThread.a"
    "$OPENCV_STATIC_LIB_DIR/libHalf.a"
    # 其他可能的依赖
    "$OPENCV_STATIC_LIB_DIR/libjbig.a"
    "$OPENCV_STATIC_LIB_DIR/libLerc.a"
    "$OPENCV_STATIC_LIB_DIR/libdeflate.a"
    "$OPENCV_STATIC_LIB_DIR/liblzma.a"
    "$OPENCV_STATIC_LIB_DIR/libzstd.a"
)

FOUND_DEPS=()
for dep in "${OPENCV_DEPS[@]}"; do
    if [ -f "$dep" ]; then
        FOUND_DEPS+=("$dep")
        echo "✓ 找到依赖库: $(basename "$dep")"
    else
        echo "⚠️  未找到依赖库: $(basename "$dep")"
    fi
done

echo "✓ 找到 ${#FOUND_DEPS[@]} 个依赖库"

# 临时禁用系统OpenCV
echo "========================================"
echo "临时禁用系统OpenCV"
echo "========================================"

# 备份系统OpenCV库
BACKUP_DIR="/tmp/opencv_backup_$$"
mkdir -p "$BACKUP_DIR"

if [ -f "/mingw64/lib/libopencv_core.dll.a" ]; then
    echo "⚠️  临时移动系统OpenCV库..."
    mv /mingw64/lib/libopencv_*.dll.a "$BACKUP_DIR/" 2>/dev/null || true
    mv /mingw64/lib/libopencv_*.a "$BACKUP_DIR/" 2>/dev/null || true
    echo "✓ 系统OpenCV库已临时移动"
fi

# 开始编译
echo "========================================"
echo "开始编译"
echo "========================================"

# 编译命令 - 明确指定静态库路径
COMPILE_CMD=(
    "$CXX"
    $CXXFLAGS
    $LDFLAGS
    -shared
    -o "dataset_export.dll"
    "${FOUND_SOURCES[@]}"
    # 明确指定静态OpenCV库的完整路径
    "$OPENCV_CORE_LIB"
    "$OPENCV_IMGPROC_LIB"
    "$OPENCV_IMGCODECS_LIB"
    # 添加依赖库
    "${FOUND_DEPS[@]}"
    # Windows系统库
    -lole32 -loleaut32 -luuid -lgdi32 -luser32
    -lkernel32 -ladvapi32 -lws2_32 -lwsock32
    -lopengl32 -lglu32 -lcomctl32 -lcomdlg32
    -lshell32 -lvfw32
)

echo "编译命令:"
printf '%s\n' "${COMPILE_CMD[@]}"
echo ""

echo "开始编译..."
"${COMPILE_CMD[@]}"

COMPILE_RESULT=$?

# 恢复系统OpenCV库
echo "========================================"
echo "恢复系统环境"
echo "========================================"

if [ -d "$BACKUP_DIR" ] && [ "$(ls -A "$BACKUP_DIR")" ]; then
    echo "恢复系统OpenCV库..."
    mv "$BACKUP_DIR"/* /mingw64/lib/ 2>/dev/null || true
    rmdir "$BACKUP_DIR"
    echo "✓ 系统OpenCV库已恢复"
fi

if [ $COMPILE_RESULT -ne 0 ]; then
    echo "❌ 编译失败！"
    echo ""
    echo "可能的原因："
    echo "1. 缺少必要的依赖库"
    echo "2. 静态库版本不兼容"
    echo "3. 链接顺序问题"
    echo "4. 头文件路径问题"
    exit 1
fi

echo "✅ 编译成功！"

# 检查生成的DLL
echo "========================================"
echo "检查编译结果"
echo "========================================"

if [ ! -f "dataset_export.dll" ]; then
    echo "❌ 未找到生成的DLL文件"
    exit 1
fi

echo "✓ 找到生成的DLL: dataset_export.dll"
echo "文件大小: $(ls -lh dataset_export.dll | awk '{print $5}')"

# 检查依赖关系
echo ""
echo "检查DLL依赖关系:"
if command -v ldd >/dev/null 2>&1; then
    echo "所有依赖："
    ldd dataset_export.dll | head -20
    
    echo ""
    echo "OpenCV相关依赖："
    OPENCV_DEPS_CHECK=$(ldd dataset_export.dll | grep -i opencv || echo "无")
    if [ "$OPENCV_DEPS_CHECK" = "无" ]; then
        echo "🎉 太好了！没有发现OpenCV动态库依赖！"
        echo "这意味着OpenCV已经静态链接到DLL中了！"
    else
        echo "❌ 仍然发现OpenCV动态库依赖："
        echo "$OPENCV_DEPS_CHECK"
    fi
    
    echo ""
    echo "MinGW运行时依赖："
    MINGW_DEPS=$(ldd dataset_export.dll | grep -E "(libgcc|libstdc|libwinpthread)" || echo "无")
    if [ "$MINGW_DEPS" = "无" ]; then
        echo "🎉 太好了！没有发现MinGW运行时依赖！"
    else
        echo "⚠️  发现MinGW运行时依赖："
        echo "$MINGW_DEPS"
    fi
else
    echo "ldd命令不可用，使用objdump检查..."
    if command -v objdump >/dev/null 2>&1; then
        echo "DLL依赖："
        objdump -p dataset_export.dll | grep "DLL Name:" | sort
        
        echo ""
        echo "检查OpenCV依赖："
        OPENCV_CHECK=$(objdump -p dataset_export.dll | grep "DLL Name:" | grep -i opencv || echo "无")
        if [ "$OPENCV_CHECK" = "无" ]; then
            echo "🎉 太好了！没有发现OpenCV动态库依赖！"
        else
            echo "❌ 仍然发现OpenCV动态库依赖："
            echo "$OPENCV_CHECK"
        fi
    fi
fi

# 复制到上级目录
echo ""
echo "复制DLL到项目目录..."
cp dataset_export.dll "../lib/"
cp dataset_export.dll "../build/bin/"
echo "✓ 已复制到 ../lib/ 和 ../build/bin/ 目录"

# 显示总结
echo ""
echo "========================================"
echo "编译完成总结"
echo "========================================"
echo "✅ 使用静态OpenCV重新编译成功！"
echo "📁 DLL位置: $(pwd)/dataset_export.dll"
echo "📁 备份位置: ../lib/dataset_export.dll"
echo "📁 构建位置: ../build/bin/dataset_export.dll"
echo "🔧 使用的OpenCV: $OPENCV_STATIC_ROOT"
echo "💾 文件大小: $(ls -lh dataset_export.dll | awk '{print $5}')"

echo ""
echo "下一步："
echo "1. 运行 ../check-dependencies.sh 检查依赖关系"
echo "2. 运行 ../auto-deploy-dll.sh 部署到Java项目"
echo "3. 测试DLL功能"

echo ""
echo "🎉 静态OpenCV重新编译完成！"
