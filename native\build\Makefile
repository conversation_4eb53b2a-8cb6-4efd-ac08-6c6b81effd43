# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\code\java\data-annotation-platform\native

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\code\java\data-annotation-platform\native\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	C:\msys64\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\msys64\mingw64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	C:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	C:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	C:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	C:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	C:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	C:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\code\java\data-annotation-platform\native\build\CMakeFiles C:\work\code\java\data-annotation-platform\native\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\code\java\data-annotation-platform\native\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named dataset_export

# Build rule for target.
dataset_export: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 dataset_export
.PHONY : dataset_export

# fast build rule for target.
dataset_export/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/build
.PHONY : dataset_export/fast

src/coco_exporter.obj: src/coco_exporter.cpp.obj
.PHONY : src/coco_exporter.obj

# target to build an object file
src/coco_exporter.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj
.PHONY : src/coco_exporter.cpp.obj

src/coco_exporter.i: src/coco_exporter.cpp.i
.PHONY : src/coco_exporter.i

# target to preprocess a source file
src/coco_exporter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.i
.PHONY : src/coco_exporter.cpp.i

src/coco_exporter.s: src/coco_exporter.cpp.s
.PHONY : src/coco_exporter.s

# target to generate assembly for a file
src/coco_exporter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.s
.PHONY : src/coco_exporter.cpp.s

src/dataset_export_jni.obj: src/dataset_export_jni.cpp.obj
.PHONY : src/dataset_export_jni.obj

# target to build an object file
src/dataset_export_jni.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj
.PHONY : src/dataset_export_jni.cpp.obj

src/dataset_export_jni.i: src/dataset_export_jni.cpp.i
.PHONY : src/dataset_export_jni.i

# target to preprocess a source file
src/dataset_export_jni.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.i
.PHONY : src/dataset_export_jni.cpp.i

src/dataset_export_jni.s: src/dataset_export_jni.cpp.s
.PHONY : src/dataset_export_jni.s

# target to generate assembly for a file
src/dataset_export_jni.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.s
.PHONY : src/dataset_export_jni.cpp.s

src/image_crop.obj: src/image_crop.cpp.obj
.PHONY : src/image_crop.obj

# target to build an object file
src/image_crop.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj
.PHONY : src/image_crop.cpp.obj

src/image_crop.i: src/image_crop.cpp.i
.PHONY : src/image_crop.i

# target to preprocess a source file
src/image_crop.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/image_crop.cpp.i
.PHONY : src/image_crop.cpp.i

src/image_crop.s: src/image_crop.cpp.s
.PHONY : src/image_crop.s

# target to generate assembly for a file
src/image_crop.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/image_crop.cpp.s
.PHONY : src/image_crop.cpp.s

src/image_processor.obj: src/image_processor.cpp.obj
.PHONY : src/image_processor.obj

# target to build an object file
src/image_processor.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj
.PHONY : src/image_processor.cpp.obj

src/image_processor.i: src/image_processor.cpp.i
.PHONY : src/image_processor.i

# target to preprocess a source file
src/image_processor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/image_processor.cpp.i
.PHONY : src/image_processor.cpp.i

src/image_processor.s: src/image_processor.cpp.s
.PHONY : src/image_processor.s

# target to generate assembly for a file
src/image_processor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/image_processor.cpp.s
.PHONY : src/image_processor.cpp.s

src/minio_client.obj: src/minio_client.cpp.obj
.PHONY : src/minio_client.obj

# target to build an object file
src/minio_client.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj
.PHONY : src/minio_client.cpp.obj

src/minio_client.i: src/minio_client.cpp.i
.PHONY : src/minio_client.i

# target to preprocess a source file
src/minio_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/minio_client.cpp.i
.PHONY : src/minio_client.cpp.i

src/minio_client.s: src/minio_client.cpp.s
.PHONY : src/minio_client.s

# target to generate assembly for a file
src/minio_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/minio_client.cpp.s
.PHONY : src/minio_client.cpp.s

src/utils.obj: src/utils.cpp.obj
.PHONY : src/utils.obj

# target to build an object file
src/utils.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/utils.cpp.obj
.PHONY : src/utils.cpp.obj

src/utils.i: src/utils.cpp.i
.PHONY : src/utils.i

# target to preprocess a source file
src/utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/utils.cpp.i
.PHONY : src/utils.cpp.i

src/utils.s: src/utils.cpp.s
.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/utils.cpp.s
.PHONY : src/utils.cpp.s

src/voc_exporter.obj: src/voc_exporter.cpp.obj
.PHONY : src/voc_exporter.obj

# target to build an object file
src/voc_exporter.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj
.PHONY : src/voc_exporter.cpp.obj

src/voc_exporter.i: src/voc_exporter.cpp.i
.PHONY : src/voc_exporter.i

# target to preprocess a source file
src/voc_exporter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.i
.PHONY : src/voc_exporter.cpp.i

src/voc_exporter.s: src/voc_exporter.cpp.s
.PHONY : src/voc_exporter.s

# target to generate assembly for a file
src/voc_exporter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.s
.PHONY : src/voc_exporter.cpp.s

src/yolo_exporter.obj: src/yolo_exporter.cpp.obj
.PHONY : src/yolo_exporter.obj

# target to build an object file
src/yolo_exporter.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj
.PHONY : src/yolo_exporter.cpp.obj

src/yolo_exporter.i: src/yolo_exporter.cpp.i
.PHONY : src/yolo_exporter.i

# target to preprocess a source file
src/yolo_exporter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.i
.PHONY : src/yolo_exporter.cpp.i

src/yolo_exporter.s: src/yolo_exporter.cpp.s
.PHONY : src/yolo_exporter.s

# target to generate assembly for a file
src/yolo_exporter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.s
.PHONY : src/yolo_exporter.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... dataset_export
	@echo ... src/coco_exporter.obj
	@echo ... src/coco_exporter.i
	@echo ... src/coco_exporter.s
	@echo ... src/dataset_export_jni.obj
	@echo ... src/dataset_export_jni.i
	@echo ... src/dataset_export_jni.s
	@echo ... src/image_crop.obj
	@echo ... src/image_crop.i
	@echo ... src/image_crop.s
	@echo ... src/image_processor.obj
	@echo ... src/image_processor.i
	@echo ... src/image_processor.s
	@echo ... src/minio_client.obj
	@echo ... src/minio_client.i
	@echo ... src/minio_client.s
	@echo ... src/utils.obj
	@echo ... src/utils.i
	@echo ... src/utils.s
	@echo ... src/voc_exporter.obj
	@echo ... src/voc_exporter.i
	@echo ... src/voc_exporter.s
	@echo ... src/yolo_exporter.obj
	@echo ... src/yolo_exporter.i
	@echo ... src/yolo_exporter.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

