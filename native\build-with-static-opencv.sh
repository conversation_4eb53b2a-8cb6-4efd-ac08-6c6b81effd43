#!/bin/bash

# 使用用户编译的静态OpenCV进行完全静态编译
# 这个脚本会使用您在 C:\dev\opencv_static 编译的静态OpenCV库

echo "========================================"
echo "使用静态OpenCV进行完全静态编译"
echo "========================================"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ 请在MSYS2环境中运行此脚本"
    exit 1
fi

echo "✓ MSYS2环境: $MSYSTEM"

# 检查用户编译的静态OpenCV
OPENCV_STATIC_ROOT="C:/dev/opencv_static"
OPENCV_STATIC_LIB_DIR="$OPENCV_STATIC_ROOT/x64/mingw/staticlib"
OPENCV_STATIC_INCLUDE_DIR="$OPENCV_STATIC_ROOT/include"

echo "========================================"
echo "检查静态OpenCV库"
echo "========================================"

if [ ! -d "$OPENCV_STATIC_ROOT" ]; then
    echo "❌ 未找到静态OpenCV目录: $OPENCV_STATIC_ROOT"
    echo "请确保您已经按照教程编译了静态版本的OpenCV"
    exit 1
fi

if [ ! -d "$OPENCV_STATIC_LIB_DIR" ]; then
    echo "❌ 未找到静态库目录: $OPENCV_STATIC_LIB_DIR"
    echo "请检查OpenCV编译输出目录结构"
    exit 1
fi

echo "✓ 找到静态OpenCV根目录: $OPENCV_STATIC_ROOT"
echo "✓ 找到静态库目录: $OPENCV_STATIC_LIB_DIR"

# 列出可用的静态库
echo ""
echo "可用的OpenCV静态库文件:"
ls -la "$OPENCV_STATIC_LIB_DIR" | grep "\.a$" | head -10
if [ $(ls -1 "$OPENCV_STATIC_LIB_DIR"/*.a 2>/dev/null | wc -l) -gt 10 ]; then
    echo "... 还有 $(($(ls -1 "$OPENCV_STATIC_LIB_DIR"/*.a | wc -l) - 10)) 个库文件"
fi

# 检查关键库文件
echo ""
echo "检查关键OpenCV库文件:"
REQUIRED_LIBS=("opencv_core" "opencv_imgproc" "opencv_imgcodecs")
ALL_FOUND=true

for lib in "${REQUIRED_LIBS[@]}"; do
    if ls "$OPENCV_STATIC_LIB_DIR"/lib${lib}*.a >/dev/null 2>&1; then
        lib_file=$(ls "$OPENCV_STATIC_LIB_DIR"/lib${lib}*.a | head -1)
        echo "✓ 找到: $(basename "$lib_file")"
    else
        echo "❌ 未找到: lib${lib}*.a"
        ALL_FOUND=false
    fi
done

if [ "$ALL_FOUND" = false ]; then
    echo ""
    echo "❌ 缺少必要的OpenCV库文件"
    echo "请检查OpenCV编译是否成功完成"
    exit 1
fi

echo ""
echo "✅ 所有必要的OpenCV静态库都已找到"

# 检查系统依赖
echo "========================================"
echo "检查系统依赖"
echo "========================================"

# 检查必要的系统包
REQUIRED_PACKAGES=(
    "mingw-w64-x86_64-gcc"
    "mingw-w64-x86_64-cmake" 
    "mingw-w64-x86_64-make"
    "mingw-w64-x86_64-nlohmann-json"
)

for pkg in "${REQUIRED_PACKAGES[@]}"; do
    if pacman -Q "$pkg" >/dev/null 2>&1; then
        echo "✓ $pkg 已安装"
    else
        echo "❌ $pkg 未安装"
        echo "正在安装 $pkg..."
        pacman -S --noconfirm "$pkg"
    fi
done

# 检查vcpkg（用于MinIO）
echo ""
echo "检查vcpkg支持:"
VCPKG_ROOT=${VCPKG_ROOT:-"$HOME/vcpkg"}
if [ -f "$VCPKG_ROOT/vcpkg.exe" ]; then
    echo "✓ 找到vcpkg: $VCPKG_ROOT"
    if $VCPKG_ROOT/vcpkg.exe list | grep -q "minio-cpp:x64-mingw-static"; then
        echo "✓ MinIO静态库已安装"
        MINIO_AVAILABLE=true
    else
        echo "⚠️  MinIO静态库未安装，将跳过MinIO支持"
        MINIO_AVAILABLE=false
    fi
else
    echo "⚠️  vcpkg未找到，将跳过MinIO支持"
    MINIO_AVAILABLE=false
fi

# 创建并进入构建目录
echo "========================================"
echo "配置构建环境"
echo "========================================"

BUILD_DIR="build_static_opencv"
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo "✓ 创建构建目录: $BUILD_DIR"

# 设置环境变量
export CC=/mingw64/bin/gcc.exe
export CXX=/mingw64/bin/g++.exe

echo "✓ 设置编译器: $CC, $CXX"

# CMake配置参数
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC=ON
    -DCMAKE_C_COMPILER="$CC"
    -DCMAKE_CXX_COMPILER="$CXX"
    -DCMAKE_MAKE_PROGRAM=/mingw64/bin/mingw32-make.exe
    
    # 强制静态链接
    -DCMAKE_FIND_LIBRARY_SUFFIXES=".a"
    -DBUILD_SHARED_LIBS=OFF
    -DCMAKE_C_FLAGS="-static"
    -DCMAKE_CXX_FLAGS="-static"
    -DCMAKE_EXE_LINKER_FLAGS="-static"
    -DCMAKE_SHARED_LINKER_FLAGS="-static"
    
    # 指定静态OpenCV路径
    -DOPENCV_STATIC_ROOT="$OPENCV_STATIC_ROOT"
)

# 如果有vcpkg，添加vcpkg支持
if [ "$MINIO_AVAILABLE" = true ]; then
    CMAKE_ARGS+=(
        -DCMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
        -DVCPKG_TARGET_TRIPLET="x64-mingw-static"
    )
    echo "✓ 启用MinIO支持"
else
    echo "⚠️  跳过MinIO支持"
fi

# 运行CMake配置
echo "========================================"
echo "运行CMake配置"
echo "========================================"

echo "CMake参数:"
for arg in "${CMAKE_ARGS[@]}"; do
    echo "  $arg"
done

echo ""
echo "开始配置..."

/mingw64/bin/cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败！"
    echo ""
    echo "可能的原因："
    echo "1. 静态OpenCV库路径不正确"
    echo "2. 缺少必要的依赖库"
    echo "3. 编译器配置问题"
    echo ""
    echo "请检查上面的错误信息"
    exit 1
fi

echo "✅ CMake配置成功！"

# 开始编译
echo "========================================"
echo "开始编译"
echo "========================================"

echo "使用 $(nproc) 个并行任务进行编译..."

/mingw64/bin/mingw32-make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    echo ""
    echo "可能的原因："
    echo "1. 静态库链接错误"
    echo "2. 缺少依赖库"
    echo "3. 内存不足"
    echo ""
    echo "请检查上面的错误信息"
    exit 1
fi

echo "✅ 编译成功！"

# 检查生成的DLL
echo "========================================"
echo "检查编译结果"
echo "========================================"

# 查找生成的DLL
DLL_PATHS=("bin/dataset_export.dll" "lib/dataset_export.dll")
DLL_FILE=""

for path in "${DLL_PATHS[@]}"; do
    if [ -f "$path" ]; then
        DLL_FILE="$path"
        break
    fi
done

if [ -z "$DLL_FILE" ]; then
    echo "❌ 未找到生成的DLL文件"
    echo "可用文件："
    find . -name "*.dll" -o -name "*.so" | head -10
    exit 1
fi

echo "✓ 找到生成的DLL: $DLL_FILE"
echo "文件大小: $(ls -lh "$DLL_FILE" | awk '{print $5}')"

# 检查依赖关系
echo ""
echo "检查DLL依赖关系:"
if command -v ldd >/dev/null 2>&1; then
    echo "所有依赖："
    ldd "$DLL_FILE" | head -15
    
    echo ""
    echo "非系统依赖："
    NON_SYSTEM_DEPS=$(ldd "$DLL_FILE" | grep -v -E "^.*DLL Name: (KERNEL32|USER32|ADVAPI32|msvcrt|WS2_32|WINMM|CRYPT32|bcrypt|Secur32|IPHLPAPI|WSOCK32|GLU32|dxcore|IMM32|CRYPTBASE|bcryptPrimitives|ntdll|KERNELBASE|sechost|RPCRT4|ucrtbase|win32u|GDI32|gdi32full|msvcp_win|combase|SSPICLI|OPENGL32)\.dll$" | grep "not found\|mingw64")
    
    if [ -n "$NON_SYSTEM_DEPS" ]; then
        echo "$NON_SYSTEM_DEPS"
        echo ""
        echo "⚠️  仍然有非系统依赖！"
        echo "这意味着不是完全静态编译。"
    else
        echo "无"
        echo ""
        echo "🎉 恭喜！这是真正的完全静态编译！"
        echo "只依赖Windows系统库，可以直接复制到其他机器使用。"
    fi
else
    echo "ldd命令不可用，使用objdump检查..."
    if command -v objdump >/dev/null 2>&1; then
        objdump -p "$DLL_FILE" | grep "DLL Name:" | sort
    fi
fi

# 复制到上级目录
echo ""
echo "复制DLL到项目目录..."
cp "$DLL_FILE" "../lib/"
echo "✓ 已复制到 ../lib/ 目录"

# 显示总结
echo ""
echo "========================================"
echo "编译完成总结"
echo "========================================"
echo "✅ 使用静态OpenCV编译成功！"
echo "📁 DLL位置: $(pwd)/$DLL_FILE"
echo "📁 备份位置: ../lib/$(basename "$DLL_FILE")"
echo "🔧 使用的OpenCV: $OPENCV_STATIC_ROOT"
echo "💾 文件大小: $(ls -lh "$DLL_FILE" | awk '{print $5}')"

if [ "$MINIO_AVAILABLE" = true ]; then
    echo "🌐 MinIO支持: 已启用"
else
    echo "🌐 MinIO支持: 未启用"
fi

echo ""
echo "下一步："
echo "1. 运行 ../auto-deploy-dll.sh 部署到Java项目"
echo "2. 或者运行 ../check-dependencies.sh 检查依赖关系"
echo "3. 测试DLL功能"

echo ""
echo "🎉 完全静态编译完成！"
