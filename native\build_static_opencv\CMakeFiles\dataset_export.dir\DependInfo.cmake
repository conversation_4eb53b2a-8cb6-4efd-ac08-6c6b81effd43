
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/work/code/java/data-annotation-platform/native/src/coco_exporter.cpp" "CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/dataset_export_jni.cpp" "CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/image_crop.cpp" "CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/image_processor.cpp" "CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/minio_client.cpp" "CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/utils.cpp" "CMakeFiles/dataset_export.dir/src/utils.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/utils.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/voc_exporter.cpp" "CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/src/yolo_exporter.cpp" "CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj" "gcc" "CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
