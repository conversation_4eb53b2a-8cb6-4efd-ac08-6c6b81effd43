#!/bin/bash

# 完全静态编译脚本 - 确保零依赖的可执行文件

echo "========================================"
echo "完全静态编译 - 零依赖构建"
echo "========================================"

# 强制静态编译
export BUILD_STATIC=ON

# 设置编译器环境变量，强制静态链接
export CC="/mingw64/bin/gcc.exe"
export CXX="/mingw64/bin/g++.exe"
export CFLAGS="-static-libgcc"
export CXXFLAGS="-static-libgcc -static-libstdc++"
export LDFLAGS="-static-libgcc -static-libstdc++ -static"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ 请在MSYS2环境中运行此脚本"
    exit 1
fi

echo "✓ MSYS2环境: $MSYSTEM"

# 创建构建目录
mkdir -p build_static
cd build_static

# 清理之前的构建
rm -f CMakeCache.txt
rm -rf CMakeFiles

echo "========================================"
echo "配置CMake - 完全静态编译"
echo "========================================"

# CMake配置参数
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC=ON
    -DCMAKE_C_COMPILER=/mingw64/bin/gcc.exe
    -DCMAKE_CXX_COMPILER=/mingw64/bin/g++.exe
    -DCMAKE_MAKE_PROGRAM=/mingw64/bin/mingw32-make.exe
    
    # 强制静态链接
    -DCMAKE_FIND_LIBRARY_SUFFIXES=".a"
    -DBUILD_SHARED_LIBS=OFF
    -DCMAKE_EXE_LINKER_FLAGS="-static-libgcc -static-libstdc++ -static"
    -DCMAKE_SHARED_LINKER_FLAGS="-static-libgcc -static-libstdc++ -static"
    
    # 禁用RPATH
    -DCMAKE_SKIP_RPATH=TRUE
    -DCMAKE_SKIP_BUILD_RPATH=TRUE
)

# 如果有vcpkg，添加vcpkg支持
VCPKG_ROOT=${VCPKG_ROOT:-"$HOME/vcpkg"}
if [ -f "$VCPKG_ROOT/vcpkg.exe" ]; then
    echo "✓ 使用vcpkg进行静态链接"
    CMAKE_ARGS+=(
        -DCMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
        -DVCPKG_TARGET_TRIPLET="x64-mingw-static"
    )
fi

# 运行CMake配置
echo "运行CMake配置..."
/mingw64/bin/cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败！"
    exit 1
fi

echo "========================================"
echo "开始编译..."
echo "========================================"

# 编译
/mingw64/bin/mingw32-make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    exit 1
fi

echo "========================================"
echo "编译完成！检查结果..."
echo "========================================"

# 查找生成的DLL
DLL_FILE=""
if [ -f "bin/dataset_export.dll" ]; then
    DLL_FILE="bin/dataset_export.dll"
elif [ -f "lib/dataset_export.dll" ]; then
    DLL_FILE="lib/dataset_export.dll"
fi

if [ -n "$DLL_FILE" ]; then
    echo "✓ 找到生成的DLL: $DLL_FILE"
    echo "文件大小: $(ls -lh "$DLL_FILE" | awk '{print $5}')"
    
    # 检查依赖
    echo ""
    echo "检查DLL依赖关系:"
    if command -v objdump >/dev/null 2>&1; then
        objdump -p "$DLL_FILE" | grep "DLL Name:" | sort
        
        # 检查MinGW依赖
        MINGW_DEPS=$(objdump -p "$DLL_FILE" | grep "DLL Name:" | grep -E "(libgcc|libstdc|libwinpthread)")
        if [ -n "$MINGW_DEPS" ]; then
            echo ""
            echo "⚠️  警告：仍然有MinGW运行时依赖："
            echo "$MINGW_DEPS"
            echo "这不是完全静态编译！"
        else
            echo ""
            echo "✅ 恭喜！未发现MinGW运行时依赖，这可能是完全静态编译！"
        fi
    fi
    
    # 复制到上级目录
    cp "$DLL_FILE" "../lib/"
    echo "✓ 已复制到 ../lib/ 目录"
    
else
    echo "❌ 未找到生成的DLL文件"
fi

echo ""
echo "========================================"
echo "构建完成！"
echo "========================================"
echo "请运行 ../check-dependencies.sh 来详细检查依赖关系"
echo "或运行 ../test-portability.bat 来测试便携性"
