# Dataset Export DLL 部署包

这是一个完整的部署包，包含了运行 dataset_export.dll 所需的所有文件。

## 📁 文件列表

- `dataset_export.dll` - 主要的JNI库
- `*.dll` - 依赖库文件
- `TestDLL.java` - Java测试程序
- `test.bat` - Windows测试脚本
- `test.sh` - Linux测试脚本
- `README.md` - 本说明文件

## 🚀 使用方法

### 方法1：快速测试
1. 将整个目录复制到目标机器
2. 在Windows上运行 `test.bat`
3. 在Linux上运行 `./test.sh`

### 方法2：在Java项目中使用
1. 将所有DLL文件复制到Java项目的根目录或lib目录
2. 在Java代码中加载库：
   ```java
   System.loadLibrary("dataset_export");
   ```

### 方法3：设置系统路径
1. 将DLL文件目录添加到系统PATH环境变量
2. 或者在Java启动时指定：
   ```bash
   java -Djava.library.path=/path/to/dlls YourMainClass
   ```

## 📋 系统要求

- **操作系统**: Windows 7/8/10/11 (64位)
- **Java**: JRE 8 或更高版本
- **架构**: x64

## 📊 部署统计

- **成功收集**: 0 个DLL文件
- **缺失文件**: 0 个文件
- **总大小**: 13M

## 🔧 故障排除

### 问题1：DLL加载失败
- 确保所有DLL文件在同一目录下
- 检查目标机器是否为64位Windows
- 确保Java能找到DLL文件

### 问题2：缺少依赖
- 安装 Microsoft Visual C++ Redistributable
- 确保Windows系统是最新的

### 问题3：权限问题
- 以管理员身份运行
- 检查文件权限设置

## 📞 技术支持

如果遇到问题，请检查：
1. Java版本是否兼容
2. 系统架构是否匹配
3. 所有DLL文件是否完整

## 📝 版本信息

- **构建时间**: 2025年08月 7日 20:10:22
- **构建环境**: MSYS2/MinGW64
- **编译器**: GCC 
